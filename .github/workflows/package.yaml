---
name: Package
on:
  push:
    branches:
      - 'main'
    tags:
      - 'v*'
  pull_request:
    branches:
      - 'main'

jobs:
  test:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run linting
        run: npm run lint
      - name: Run unit tests
        run: npm run test:unit -- --run
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      - name: Build application for testing
        run: npm run build
        env:
          VITE_API_BASE_DOMAIN: uphi.cc
          VITE_PRODUCTION: false
          VITE_MS_FLEET_URL: https://glider.uphi.cc/api
          VITE_MS_RIDES_URL: https://ride.uphi.cc
          VITE_MS_AUTH_URL: https://auth.uphi.cc
          VITE_IP_CONNECTOR_URL: https://ips.uphi.cc
          VITE_NEWBORN_URL: https://newborn.uphi.cc
          VITE_FLIGHT_REVIEW_URL: https://flight-review.uphi.cc
          VITE_MICROSOFT_URL: https://microsoft.uphi.cc
      - name: Run E2E tests (Playwright)
        run: npm run test:e2e
        env:
          PUBLIC_AUTH_CLIENT_ID: svelte-app-test
          PUBLIC_AUTH_REALM: jedsy
          PUBLIC_AUTH_URL: https://auth.uphi.cc
          PUBLIC_GLIDER_MS_BACKEND_URL: https://glider.uphi.cc/api
          PUBLIC_MICROSOFT_MS_BACKEND_URL: https://microsoft.uphi.cc
          PUBLIC_FLIGHT_REVIEW_BACKEND_URL: https://flight-review.uphi.cc
          PUBLIC_API_BASE_DOMAIN: uphi.cc

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  docker:
    needs: test
    runs-on: ubuntu-24.04
    outputs:
      image_digest: ${{ steps.build_push.outputs.digest }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{ secrets.AWS_ECR_PUSH_ROLE }}
          aws-region: ${{ vars.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver: docker
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ vars.AWS_REGISTRY_URL }}
          tags: |
            type=raw,value=latest,enable={{is_default_branch}}
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
      - id: build_push
        name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  deploy:
    needs: docker
    runs-on: ubuntu-24.04
    if: startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/main'
    steps:
      - if: github.ref == 'refs/heads/main'
        run: echo "MS_ENV=dev" >> "$GITHUB_ENV"
      - if: startsWith(github.ref, 'refs/tags/v')
        run: echo "MS_ENV=prod" >> "$GITHUB_ENV"
      - name: Trigger workflow in IAC repo
        run: |
          curl \
          -sSL \
          --fail \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $IAC_WORKFLOW_TRIGGER_TOKEN" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          "https://api.github.com/repos/PackageGlider/iac/actions/workflows/deploy.yaml/dispatches" \
          -d "{\"ref\":\"main\",\"inputs\":{\"name\":\"$MS_NAME\",\"digest\":\"$MS_DIGEST\",\"environment\":\"$MS_ENV\"}}"
        env:
          IAC_WORKFLOW_TRIGGER_TOKEN: ${{ secrets.IAC_WORKFLOW_TRIGGER_TOKEN }}
          MS_NAME: ${{ github.event.repository.name }}
          MS_DIGEST: ${{ needs.docker.outputs.image_digest }}
