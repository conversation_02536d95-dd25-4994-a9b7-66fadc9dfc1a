import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';

export default defineConfig({
	plugins: [sveltekit()],
	test: {
		include: ['src/**/*.{test,spec}.{js,ts}', 'src/__tests__/**/*.{test,spec}.{js,ts}'],
		environment: 'jsdom',
		globals: true,
		setupFiles: ['src/__tests__/setup.ts'],
		coverage: {
			reporter: ['text', 'json', 'html'],
			exclude: ['node_modules/', 'src/__tests__/', '***.config.*', 'build/', 'dist/']
		}
	},
	resolve: {
		alias: {
			$lib: '/src/lib',
			$styles: '/src/styles'
		}
	}
});
