import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';
import preprocess from 'svelte-preprocess';

const config = {
	kit: {
		adapter: adapter({
			trailingSlash: 'always',
			fallback: 'index.html'
		}),
		alias: {
			$components: 'src/lib/components',
			$services: 'src/lib/services',
			$stores: 'src/lib/stores',
			$types: 'src/lib/types',
			$styles: 'src/styles'
		}
	},
	preprocess: [
		vitePreprocess(),
		preprocess({
			scss: true
		})
	]
};

export default config;
