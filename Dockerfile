FROM node:22-alpine as builder

WORKDIR /usr/src/app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

# The Base path is now hardcoded in the Dockerfile since we only use Docker for the actual deployment
RUN VITE_PRODUCTION="$VITE_PRODUCTION" \
    VITE_MS_FLEET_URL="$VITE_MS_FLEET_URL" \
    VITE_MS_RIDES_URL="$VITE_MS_RIDES_URL" \
    VITE_MS_AUTH_URL="$VITE_MS_AUTH_URL" \
    VITE_IP_CONNECTOR_URL="$VITE_IP_CONNECTOR_URL" \
    VITE_NEWBORN_GLIDER_URL="$VITE_NEWBORN_GLIDER_URL" \
    VITE_FLIGHT_REVIEW_URL="$VITE_FLIGHT_REVIEW_URL" \
    VITE_MICROSOFT_URL="$VITE_MICROSOFT_URL" \
    npm run build

FROM flashspys/nginx-static:latest
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/build /static
COPY entrypoint.sh /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"] 
