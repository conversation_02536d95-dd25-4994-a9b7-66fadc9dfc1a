import { describe, it, expect } from 'vitest';
import { formatDate, formatTime, formatDateTime } from '$lib/utils/datetime';

describe('DateTime Utils', () => {
	describe('formatDate', () => {
		it('should format UTC date to GMT+2 date', () => {
			const utcDate = '2024-01-15T10:00:00Z';
			const result = formatDate(utcDate);
			expect(result).toBe('15.01.2024');
		});

		it('should return "-" for empty string', () => {
			expect(formatDate('')).toBe('-');
		});

		it('should return "-" for invalid date', () => {
			expect(formatDate('invalid-date')).toBe('-');
		});
	});

	describe('formatTime', () => {
		it('should format UTC time to configured timezone', () => {
			const utcTime = '2024-01-15T10:00:00Z';
			const result = formatTime(utcTime);
			expect(result).toMatch(/^\d{2}:\d{2}$/);
		});

		it('should handle custom timezone parameter', () => {
			const utcTime = '2024-01-15T10:00:00Z';
			const result = formatTime(utcTime, 'America/New_York');
			expect(result).toBe('05:00');
		});

		it('should handle different timezone parameter', () => {
			const utcTime = '2024-07-15T10:00:00Z';
			const result = formatTime(utcTime, 'Africa/Blantyre');
			expect(result).toBe('12:00');
		});

		it('should return "-" for empty string', () => {
			expect(formatTime('')).toBe('-');
		});

		it('should return "-" for invalid date', () => {
			expect(formatTime('invalid-date')).toBe('-');
		});
	});

	describe('formatDateTime', () => {
		it('should format UTC datetime to configured timezone', () => {
			const utcDateTime = '2024-01-15T10:30:45Z';
			const result = formatDateTime(utcDateTime);
			expect(result).toMatch(/^\d{2}\.\d{2}\.\d{4}, \d{2}:\d{2}:\d{2}$/);
		});

		it('should format UTC datetime with custom timezone', () => {
			const utcDateTime = '2024-01-15T10:30:45Z';
			const result = formatDateTime(utcDateTime, 'Africa/Blantyre');
			expect(result).toBe('15.01.2024, 12:30:45');
		});

		it('should return "N/A" for empty string', () => {
			expect(formatDateTime('')).toBe('N/A');
		});

		it('should return "N/A" for invalid date', () => {
			expect(formatDateTime('invalid-date')).toBe('N/A');
		});
	});

	describe('getUserTimezone (implicit)', () => {
		it('should use browser timezone automatically in formatDate', () => {
			const result = formatDate('2024-01-15T10:00:00Z');
			// Should return a valid date format, proving timezone detection works
			expect(result).toMatch(/^\d{2}\.\d{2}\.\d{4}$/);
		});

		it('should use browser timezone automatically in formatTime', () => {
			const result = formatTime('2024-01-15T10:00:00Z');
			// Should return a valid time format, proving timezone detection works
			expect(result).toMatch(/^\d{2}:\d{2}$/);
		});
	});
});
