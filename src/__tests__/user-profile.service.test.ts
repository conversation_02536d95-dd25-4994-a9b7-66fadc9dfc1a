import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { UserProfileService } from '$lib/services/user-profile.service';
import { userProfile, userRoles, isUserAdmin, keycloakClient } from '$lib/stores';
import { get } from 'svelte/store';
import type { KeycloakProfile } from 'keycloak-js';

const mockKeycloak = {
	authenticated: true,
	token: 'mock-token',
	refreshToken: 'mock-refresh-token',
	realmAccess: { roles: ['user', 'admin'] },
	resourceAccess: {
		'admin-ui': { roles: ['manage-users'] }
	},
	loadUserProfile: vi.fn(),
	logout: vi.fn(),
	updateToken: vi.fn()
};

const localStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
	value: localStorageMock
});

describe('UserProfileService', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		keycloakClient.set(mockKeycloak as any);
		userProfile.set(null);
		userRoles.set([]);
		isUserAdmin.set(false);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('getUserInitials', () => {
		it('should return initials from first and last name', () => {
			const profile: KeycloakProfile = {
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>'
			};

			const result = UserProfileService.getUserInitials(profile);
			expect(result).toBe('JD');
		});

		it('should return first letter of email if no names provided', () => {
			const profile: KeycloakProfile = {
				email: '<EMAIL>'
			};

			const result = UserProfileService.getUserInitials(profile);
			expect(result).toBe('J');
		});

		it('should return "U" for null profile', () => {
			const result = UserProfileService.getUserInitials(null);
			expect(result).toBe('U');
		});

		it('should handle partial names correctly', () => {
			const profile: KeycloakProfile = {
				firstName: 'John',
				email: '<EMAIL>'
			};

			const result = UserProfileService.getUserInitials(profile);
			expect(result).toBe('J');
		});
	});

	describe('getUserDisplayName', () => {
		it('should return full name when both first and last name exist', () => {
			const profile: KeycloakProfile = {
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>'
			};

			const result = UserProfileService.getUserDisplayName(profile);
			expect(result).toBe('John Doe');
		});

		it('should return email if no names provided', () => {
			const profile: KeycloakProfile = {
				email: '<EMAIL>'
			};

			const result = UserProfileService.getUserDisplayName(profile);
			expect(result).toBe('<EMAIL>');
		});

		it('should return empty string for null profile', () => {
			const result = UserProfileService.getUserDisplayName(null);
			expect(result).toBe('');
		});
	});

	describe('getCurrentUserProfile', () => {
		it('should load and return user profile successfully', async () => {
			const mockProfile: KeycloakProfile = {
				id: '123',
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>'
			};

			mockKeycloak.loadUserProfile.mockResolvedValue(mockProfile);

			const result = await UserProfileService.getCurrentUserProfile();

			expect(mockKeycloak.loadUserProfile).toHaveBeenCalled();
			expect(result).toEqual(mockProfile);
			expect(get(userProfile)).toEqual(mockProfile);
		});

		it('should return null if keycloak not authenticated', async () => {
			keycloakClient.set({ ...mockKeycloak, authenticated: false } as any);

			const result = await UserProfileService.getCurrentUserProfile();

			expect(result).toBeNull();
			expect(mockKeycloak.loadUserProfile).not.toHaveBeenCalled();
		});

		it('should handle profile loading errors', async () => {
			mockKeycloak.loadUserProfile.mockRejectedValue(new Error('Profile load failed'));
			const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

			const result = await UserProfileService.getCurrentUserProfile();

			expect(result).toBeNull();
			expect(consoleSpy).toHaveBeenCalledWith('Failed to load user profile:', expect.any(Error));
		});
	});

	describe('getUserRoles', () => {
		it('should return combined realm and resource roles', async () => {
			const result = await UserProfileService.getUserRoles();

			expect(result).toEqual(['user', 'admin', 'manage-users']);
			expect(get(userRoles)).toEqual(['user', 'admin', 'manage-users']);
		});

		it('should return empty array if not authenticated', async () => {
			keycloakClient.set({ ...mockKeycloak, authenticated: false } as any);

			const result = await UserProfileService.getUserRoles();

			expect(result).toEqual([]);
			expect(get(userRoles)).toEqual([]);
		});
	});

	describe('isUserAdmin', () => {
		it('should return true for admin role', async () => {
			const result = await UserProfileService.isUserAdmin();

			expect(result).toBe(true);
			expect(get(isUserAdmin)).toBe(true);
		});

		it('should return true for realm-admin role', async () => {
			keycloakClient.set({
				...mockKeycloak,
				realmAccess: { roles: ['realm-admin'] },
				resourceAccess: {}
			} as any);

			const result = await UserProfileService.isUserAdmin();

			expect(result).toBe(true);
		});

		it('should return false for non-admin user', async () => {
			keycloakClient.set({
				...mockKeycloak,
				realmAccess: { roles: ['user'] },
				resourceAccess: {}
			} as any);

			const result = await UserProfileService.isUserAdmin();

			expect(result).toBe(false);
		});
	});

	describe('localStorage operations', () => {
		it('should store user data in localStorage', () => {
			const profile: KeycloakProfile = {
				id: '123',
				firstName: 'John',
				lastName: 'Doe',
				email: '<EMAIL>',
				username: 'johndoe'
			};

			UserProfileService.storeUserDataInLocalStorage(profile);

			expect(localStorageMock.setItem).toHaveBeenCalledWith(
				'user_profile',
				expect.stringContaining('"id":"123"')
			);
		});

		it('should retrieve valid user data from localStorage', () => {
			const userData = {
				id: '123',
				email: '<EMAIL>',
				timestamp: Date.now() - 30 * 60 * 1000
			};

			localStorageMock.getItem.mockReturnValue(JSON.stringify(userData));

			const result = UserProfileService.getUserDataFromLocalStorage();

			expect(result).toEqual(userData);
		});

		it('should remove expired user data from localStorage', () => {
			const expiredData = {
				id: '123',
				timestamp: Date.now() - 2 * 60 * 60 * 1000 // 2 hours ago
			};

			localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredData));

			const result = UserProfileService.getUserDataFromLocalStorage();

			expect(result).toBeNull();
			expect(localStorageMock.removeItem).toHaveBeenCalledWith('user_profile');
		});
	});

	describe('logout', () => {
		it('should clear all user data and call keycloak logout', () => {
			UserProfileService.logout();

			expect(localStorageMock.removeItem).toHaveBeenCalledWith('kc_token');
			expect(localStorageMock.removeItem).toHaveBeenCalledWith('kc_refreshToken');
			expect(localStorageMock.removeItem).toHaveBeenCalledWith('user_profile');
			expect(mockKeycloak.logout).toHaveBeenCalled();
			expect(get(userProfile)).toBeNull();
			expect(get(userRoles)).toEqual([]);
			expect(get(isUserAdmin)).toBe(false);
		});
	});
});
