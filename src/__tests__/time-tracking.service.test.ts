import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	startTimeTracking,
	stopTimeTracking,
	getLastShift,
	getShifts,
	editTimeTracking,
	type Shift,
	type TimeTrackingEvent
} from '$lib/services/time-tracking.service';
import { keycloakClient } from '$lib/stores';

global.fetch = vi.fn();

const mockKeycloak = {
	authenticated: true,
	token: 'mock-token'
};

vi.mock('$lib/environment', () => ({
	environment: {
		urlMsRides: 'https://ride.uphi.cc'
	}
}));

describe('Time Tracking Service', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		keycloakClient.set(mockKeycloak as any);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('startTimeTracking', () => {
		it('should start time tracking successfully', async () => {
			const mockResponse: TimeTrackingEvent = {
				id: 1,
				route_id: 123,
				pilot_email: '<EMAIL>',
				event: 'start',
				shift_id: 456,
				event_timestamp: '2024-01-01T10:00:00Z',
				created_at: '2024-01-01T10:00:00Z'
			};

			(fetch as any).mockResolvedValue({
				ok: true,
				json: () => Promise.resolve(mockResponse)
			});

			const result = await startTimeTracking(123, '<EMAIL>', 'Test description', 789);

			expect(fetch).toHaveBeenCalledWith(
				expect.stringContaining('/time-tracking/start'),
				expect.objectContaining({
					method: 'POST',
					headers: {
						Authorization: 'Bearer mock-token'
					}
				})
			);

			expect(result).toEqual(mockResponse);
		});

		it('should handle missing token', async () => {
			keycloakClient.set({ authenticated: false, token: null } as any);
			const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

			const result = await startTimeTracking(123, '<EMAIL>');

			expect(result).toBeNull();
			expect(consoleSpy).toHaveBeenCalledWith('Token not found');
			expect(fetch).not.toHaveBeenCalled();
		});

		it('should handle API errors', async () => {
			(fetch as any).mockResolvedValue({
				ok: false,
				status: 400,
				statusText: 'Bad Request',
				json: () => Promise.resolve({ detail: 'Active time tracking found' })
			});

			await expect(startTimeTracking(123, '<EMAIL>')).rejects.toThrow(
				'Active time tracking found'
			);
		});
	});

	describe('stopTimeTracking', () => {
		it('should stop time tracking successfully', async () => {
			const mockResponse: TimeTrackingEvent = {
				id: 2,
				route_id: 123,
				pilot_email: '<EMAIL>',
				event: 'stop',
				shift_id: 456,
				event_timestamp: '2024-01-01T11:00:00Z',
				created_at: '2024-01-01T11:00:00Z'
			};

			(fetch as any).mockResolvedValue({
				ok: true,
				json: () => Promise.resolve(mockResponse)
			});

			const result = await stopTimeTracking(456, '2024-01-01T11:00:00Z');

			expect(result).toEqual(mockResponse);
		});

		it('should handle missing token', async () => {
			keycloakClient.set({ authenticated: false, token: null } as any);
			const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

			const result = await stopTimeTracking(456);

			expect(result).toBeNull();
			expect(consoleSpy).toHaveBeenCalledWith('Token not found');
		});
	});

	describe('getLastShift', () => {
		it('should get last shift successfully', async () => {
			const mockShift: Shift = {
				pilot_email: '<EMAIL>',
				route_id: 123,
				start_time: '2024-01-01T10:00:00Z',
				stop_time: '2024-01-01T11:00:00Z',
				shift_id: 456,
				description: 'Test shift'
			};

			(fetch as any).mockResolvedValue({
				ok: true,
				json: () => Promise.resolve(mockShift)
			});

			const result = await getLastShift('<EMAIL>');

			expect(result).toEqual(mockShift);
		});
	});

	describe('editTimeTracking', () => {
		it('should edit time tracking successfully', async () => {
			const mockResponse = { success: true };
			const editRequest = {
				pilot_email: '<EMAIL>',
				route_id: 123,
				start_time: '2024-01-01T10:00:00Z',
				stop_time: '2024-01-01T11:00:00Z',
				description: 'Updated description'
			};

			(fetch as any).mockResolvedValue({
				ok: true,
				json: () => Promise.resolve(mockResponse)
			});

			const result = await editTimeTracking(456, editRequest);

			expect(result).toEqual(mockResponse);
		});
	});
});
