import { vi } from 'vitest';

Object.defineProperty(window, 'localStorage', {
	value: {
		getItem: vi.fn(),
		setItem: vi.fn(),
		removeItem: vi.fn(),
		clear: vi.fn()
	},
	writable: true
});

Object.defineProperty(window, 'sessionStorage', {
	value: {
		getItem: vi.fn(),
		setItem: vi.fn(),
		removeItem: vi.fn(),
		clear: vi.fn()
	},
	writable: true
});

global.fetch = vi.fn();

global.console = {
	...console,
	log: vi.fn(),
	debug: vi.fn(),
	info: vi.fn(),
	warn: vi.fn(),
	error: vi.fn()
};
vi.mock('$lib/environment', () => ({
	environment: {
		production: false,
		urlMsRides: 'https://ride.uphi.cc',
		urlMsAuth: 'https://auth.uphi.cc',
		gliderMsBackendUrl: 'https://glider.uphi.cc',
		authUrl: 'https://auth.uphi.cc',
		authRealm: 'jedsy',
		authClientId: 'admin-ui'
	}
}));

beforeEach(() => {
	vi.clearAllMocks();
});
