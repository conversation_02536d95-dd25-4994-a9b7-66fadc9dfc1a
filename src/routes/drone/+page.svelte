<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { derived } from 'svelte/store';
	import DronePage from '$lib/components/pages/DronePage.svelte';

	// safer extraction as a derived store
	const droneIdStore = derived(page, ($page) => $page.url.searchParams.get('id') || '');
	let droneId = '';

	const unsubscribe = droneIdStore.subscribe((id) => {
		droneId = id;
		if (!droneId) {
			goto('/drones'); // fallback
		}
	});

	onMount(() => {
		// Cleanup if needed (not strictly necessary for this use case)
		return () => unsubscribe();
	});
</script>

{#if droneId}
	<DronePage {droneId} />
{:else}
	<p class="p-4 text-red-600">No drone ID provided.</p>
{/if}
