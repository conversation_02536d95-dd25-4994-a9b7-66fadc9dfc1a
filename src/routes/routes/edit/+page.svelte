<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import {
		Button,
		Toast,
		Label,
		Input,
		Textarea,
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Modal
	} from 'flowbite-svelte';
	import { ArrowLeftOutline, EditOutline } from 'flowbite-svelte-icons';
	import { editTimeTracking, getShifts, type Shift } from '$lib/services/time-tracking.service';
	import { fetchRouteById, updateRoute } from '$lib/services/routes.service';
	import { formatDateTime } from '$lib/utils/datetime';

	let shifts: Shift[] = [];
	let routeInfo: any = null;
	let loading = true;
	let error: string | null = null;
	let routeId: number | null = null;

	let routeFormData = {
		start_location_name: '',
		end_location_name: '',
		emergency_contact: '',
		known_dangers: '',
		extra_notes: '',
		external_route_id: ''
	};
	let routeErrors: { [key: string]: string } = {};
	let savingRoute = false;
	let showRouteSuccessToast = false;

	let showEditModal = false;
	let editingShift: Shift | null = null;
	let editFormData = {
		start_time: '',
		stop_time: '',
		description: ''
	};
	let editErrors: { [key: string]: string } = {};
	let saving = false;
	let showSuccessToast = false;

	onMount(async () => {
		try {
			loading = true;

			const routeIdParam = $page.url.searchParams.get('id');

			if (!routeIdParam) {
				error = 'No route ID provided';
				return;
			}

			routeId = parseInt(routeIdParam);

			if (isNaN(routeId)) {
				error = 'Invalid route ID!';
				return;
			}

			routeInfo = await fetchRouteById(routeId);

			if (!routeInfo) {
				error = 'Route not found';
				return;
			}

			routeFormData = {
				start_location_name: routeInfo.start_location_name || '',
				end_location_name: routeInfo.end_location_name || '',
				emergency_contact: routeInfo.emergency_contact || '',
				known_dangers: routeInfo.known_dangers || '',
				extra_notes: routeInfo.extra_notes || '',
				external_route_id: routeInfo.external_route_id || ''
			};

			await loadShifts();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Unknown error loading data';
		} finally {
			loading = false;
		}
	});

	async function loadShifts() {
		try {
			const params = {
				route_id: routeId
			};
			const response = await getShifts(params);
			shifts = response || [];
		} catch (err) {}
	}

	function openEditModal(shift: Shift) {
		editingShift = shift;

		const startTime = shift.start_time ? new Date(shift.start_time).toISOString().slice(0, 16) : '';
		const stopTime = shift.stop_time ? new Date(shift.stop_time).toISOString().slice(0, 16) : '';

		editFormData = {
			start_time: startTime,
			stop_time: stopTime,
			description: shift.description || ''
		};

		editErrors = {};
		showEditModal = true;
	}

	function closeEditModal() {
		showEditModal = false;
		editingShift = null;
		editFormData = {
			start_time: '',
			stop_time: '',
			description: ''
		};
		editErrors = {};
	}

	function validateEditForm(): boolean {
		editErrors = {};

		if (!editFormData.start_time) {
			editErrors.start_time = 'Start time is required';
		}

		if (!editFormData.stop_time) {
			editErrors.stop_time = 'Stop time is required';
		}

		if (editFormData.start_time && editFormData.stop_time) {
			const startDate = new Date(editFormData.start_time);
			const stopDate = new Date(editFormData.stop_time);

			if (stopDate <= startDate) {
				editErrors.stop_time = 'Stop time must be after start time';
			}
		}

		return Object.keys(editErrors).length === 0;
	}

	const handleEditSubmit = async () => {
		if (!editingShift || !validateEditForm()) return;

		try {
			saving = true;

			const startTimeISO = new Date(editFormData.start_time).toISOString();
			const stopTimeISO = new Date(editFormData.stop_time).toISOString();

			const editData = {
				pilot_email: editingShift.pilot_email,
				route_id: editingShift.route_id,
				start_time: startTimeISO,
				stop_time: stopTimeISO,
				description: editFormData.description || undefined,
				ride_id: editingShift.ride_id || undefined
			};

			const shiftId = editingShift.shift_id || editingShift.id;
			if (!shiftId) {
				editErrors.general = 'Shift ID not found';
				return;
			}

			const result = await editTimeTracking(shiftId, editData);

			if (result) {
				showSuccessToast = true;
				closeEditModal();
				await loadShifts();
			} else {
				editErrors.general = 'Failed to update time tracking. Please try again.';
			}
		} catch (err) {
			editErrors.general =
				err instanceof Error ? err.message : 'Unknown error updating time tracking';
		} finally {
			saving = false;
		}
	};

	const handleCancel = () => {
		goto('/routes');
	};

	const hasEditError = (field: string) => {
		return editErrors[field] !== undefined;
	};

	const hasRouteError = (field: string) => {
		return routeErrors[field] !== undefined;
	};

	function validateRouteForm(): boolean {
		routeErrors = {};

		if (!routeFormData.external_route_id.trim()) {
			routeErrors.external_route_id = 'External Route ID is required';
		}

		if (!routeFormData.start_location_name.trim()) {
			routeErrors.start_location_name = 'Start location is required';
		}

		if (!routeFormData.end_location_name.trim()) {
			routeErrors.end_location_name = 'End location is required';
		}

		if (!routeFormData.emergency_contact.trim()) {
			routeErrors.emergency_contact = 'Emergency contact is required';
		}

		return Object.keys(routeErrors).length === 0;
	}

	const handleRouteSubmit = async () => {
		if (!routeId || !validateRouteForm()) return;

		try {
			savingRoute = true;
			const result = await updateRoute(routeId, routeFormData);

			if (result) {
				showRouteSuccessToast = true;
				routeInfo = await fetchRouteById(routeId);
			} else {
				routeErrors.general = 'Failed to update route. Please try again.';
			}
		} catch (err) {
			routeErrors.general = err instanceof Error ? err.message : 'Unknown error updating route';
		} finally {
			savingRoute = false;
		}
	};

	function calculateDuration(startTime: string, stopTime: string): string {
		if (!startTime || !stopTime) return '-';

		const start = new Date(startTime);
		const stop = new Date(stopTime);
		const diff = Math.floor((stop.getTime() - start.getTime()) / 1000);

		const hours = Math.floor(diff / 3600);
		const minutes = Math.floor((diff % 3600) / 60);
		const seconds = diff % 60;

		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	}
</script>

<div class="container mx-auto max-w-full px-4 py-6">
	<div class="mb-6">
		<Button color="light" class="flex items-center gap-2" on:click={handleCancel}>
			<ArrowLeftOutline class="h-4 w-4" />
			Back to Routes
		</Button>
	</div>

	<div class="rounded-lg bg-white p-6 shadow-md">
		<h1 class="mb-6 border-b pb-3 text-2xl font-bold text-gray-800">
			Edit Route & Time Tracking History
		</h1>

		{#if loading}
			<div class="flex items-center justify-center py-12">
				<div
					class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary-500 border-r-transparent align-[-0.125em]"
				></div>
				<span class="ml-3">Loading data...</span>
			</div>
		{:else if error}
			<div
				class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
				role="alert"
			>
				<p>{error}</p>
				<button class="mt-2 text-blue-500 hover:underline" on:click={handleCancel}
					>Return to routes</button
				>
			</div>
		{:else}
			<div class="mb-6 rounded-lg bg-blue-50 p-6">
				<h2 class="mb-4 text-xl font-semibold text-gray-800">Edit Route Information</h2>

				{#if routeErrors.general}
					<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
						{routeErrors.general}
					</div>
				{/if}

				<form on:submit|preventDefault={handleRouteSubmit} class="space-y-6">
					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<div class="md:col-span-2">
							<Label for="external_route_id" class="mb-2">External Route ID *</Label>
							<Input
								id="external_route_id"
								bind:value={routeFormData.external_route_id}
								placeholder="Enter external route ID"
								required
								class={hasRouteError('external_route_id') ? 'border-red-500' : ''}
							/>
							{#if hasRouteError('external_route_id')}
								<p class="mt-1 text-sm text-red-600">{routeErrors.external_route_id}</p>
							{/if}
							<p class="mt-1 text-sm text-gray-500">
								Unique identifier for this route from external documentation
							</p>
						</div>

						<div class="md:col-span-2">
							<Label for="start_location_name" class="mb-2">Start Location *</Label>
							<Textarea
								id="start_location_name"
								bind:value={routeFormData.start_location_name}
								placeholder="Enter start location details"
								rows="3"
								required
								class={hasRouteError('start_location_name') ? 'border-red-500' : ''}
							/>
							{#if hasRouteError('start_location_name')}
								<p class="mt-1 text-sm text-red-600">{routeErrors.start_location_name}</p>
							{/if}
							<p class="mt-1 text-sm text-gray-500">
								Provide detailed information about the starting point
							</p>
						</div>

						<div class="md:col-span-2">
							<Label for="end_location_name" class="mb-2">End Location *</Label>
							<Textarea
								id="end_location_name"
								bind:value={routeFormData.end_location_name}
								placeholder="Enter end location details"
								rows="3"
								required
								class={hasRouteError('end_location_name') ? 'border-red-500' : ''}
							/>
							{#if hasRouteError('end_location_name')}
								<p class="mt-1 text-sm text-red-600">{routeErrors.end_location_name}</p>
							{/if}
							<p class="mt-1 text-sm text-gray-500">
								Provide detailed information about the destination
							</p>
						</div>

						<div class="md:col-span-2">
							<Label for="emergency_contact" class="mb-2">Emergency Contact *</Label>
							<Textarea
								id="emergency_contact"
								bind:value={routeFormData.emergency_contact}
								placeholder="Enter emergency contact information"
								rows="3"
								required
								class={hasRouteError('emergency_contact') ? 'border-red-500' : ''}
							/>
							{#if hasRouteError('emergency_contact')}
								<p class="mt-1 text-sm text-red-600">{routeErrors.emergency_contact}</p>
							{/if}
							<p class="mt-1 text-sm text-gray-500">
								Include phone numbers and names of emergency contacts
							</p>
						</div>
					</div>

					<fieldset class="mt-8 border-t border-gray-200 pt-6">
						<legend class="-ml-2 px-2 text-lg font-medium text-gray-800"
							>Additional Information</legend
						>

						<div class="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
							<div class="md:col-span-2">
								<Label for="known_dangers" class="mb-2">Known Dangers</Label>
								<Textarea
									id="known_dangers"
									bind:value={routeFormData.known_dangers}
									placeholder="Enter any known dangers or hazards"
									rows="3"
								/>
							</div>

							<div class="md:col-span-2">
								<Label for="extra_notes" class="mb-2">Extra Notes</Label>
								<Textarea
									id="extra_notes"
									bind:value={routeFormData.extra_notes}
									placeholder="Enter any additional notes"
									rows="3"
								/>
							</div>
						</div>
					</fieldset>

					<div class="flex justify-end space-x-4 border-t border-gray-200 pt-6">
						<Button type="submit" color="primary" disabled={savingRoute}>
							{#if savingRoute}
								<div
									class="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
								></div>
							{/if}
							{savingRoute ? 'Saving...' : 'Save Route Changes'}
						</Button>
					</div>
				</form>
			</div>

			<div class="mb-6 rounded-lg bg-gray-50 p-4">
				<h3 class="mb-2 text-lg font-semibold text-gray-800">Route Information</h3>
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div>
						<span class="text-sm font-medium text-gray-600">Route ID:</span>
						<span class="ml-2 text-gray-800">{routeInfo?.id || 'N/A'}</span>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">External Route ID:</span>
						<span class="ml-2 text-gray-800">{routeInfo?.external_route_id || 'N/A'}</span>
					</div>
					<div class="md:col-span-2">
						<span class="text-sm font-medium text-gray-600">Route:</span>
						<span class="ml-2 text-gray-800">
							{#if routeInfo?.start_location_name && routeInfo?.end_location_name}
								{routeInfo.start_location_name} → {routeInfo.end_location_name}
							{:else}
								Route #{routeInfo?.id || 'N/A'}
							{/if}
						</span>
					</div>
				</div>
			</div>

			<div class="rounded-lg bg-green-50 p-6">
				<h2 class="mb-4 text-xl font-semibold text-gray-800">Time Tracking History</h2>
				<div class="overflow-x-auto">
					<Table>
						<TableHead>
							<TableHeadCell class="font-medium text-gray-700">Pilot Email</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Start Time</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">End Time</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Duration</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Description</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Actions</TableHeadCell>
						</TableHead>
						<TableBody>
							{#each shifts as shift}
								<TableBodyRow>
									<TableBodyCell class="font-medium text-gray-900">
										{shift.pilot_email}
									</TableBodyCell>
									<TableBodyCell>
										{formatDateTime(shift.start_time)}
									</TableBodyCell>
									<TableBodyCell>
										{formatDateTime(shift.stop_time)}
									</TableBodyCell>
									<TableBodyCell>
										{calculateDuration(shift.start_time, shift.stop_time)}
									</TableBodyCell>
									<TableBodyCell>
										{shift.description || '-'}
									</TableBodyCell>
									<TableBodyCell>
										<Button
											size="xs"
											color="blue"
											class="flex items-center gap-1"
											on:click={() => openEditModal(shift)}
										>
											<EditOutline class="h-3 w-3" />
											Edit
										</Button>
									</TableBodyCell>
								</TableBodyRow>
							{/each}
							{#if shifts.length === 0}
								<TableBodyRow>
									<TableBodyCell colspan="6" class="py-8 text-center text-gray-500">
										No time tracking records found for this route.
									</TableBodyCell>
								</TableBodyRow>
							{/if}
						</TableBody>
					</Table>
				</div>
			</div>
		{/if}
	</div>
</div>

<Modal bind:open={showEditModal} size="md" autoclose={false} on:close={closeEditModal}>
	<div class="p-4">
		<h3 class="mb-4 text-xl font-medium text-gray-900">Edit Time Tracking</h3>

		{#if editErrors.general}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{editErrors.general}
			</div>
		{/if}

		<form on:submit|preventDefault={handleEditSubmit}>
			<div class="mb-4 grid gap-4">
				<div>
					<Label for="edit_start_time" class="mb-2">Start Time *</Label>
					<Input
						id="edit_start_time"
						type="datetime-local"
						bind:value={editFormData.start_time}
						required
						class={hasEditError('start_time') ? 'border-red-500' : ''}
					/>
					{#if hasEditError('start_time')}
						<p class="mt-1 text-sm text-red-600">{editErrors.start_time}</p>
					{/if}
				</div>

				<div>
					<Label for="edit_stop_time" class="mb-2">Stop Time *</Label>
					<Input
						id="edit_stop_time"
						type="datetime-local"
						bind:value={editFormData.stop_time}
						required
						class={hasEditError('stop_time') ? 'border-red-500' : ''}
					/>
					{#if hasEditError('stop_time')}
						<p class="mt-1 text-sm text-red-600">{editErrors.stop_time}</p>
					{/if}
				</div>

				<div>
					<Label for="edit_description" class="mb-2">Description</Label>
					<Textarea
						id="edit_description"
						bind:value={editFormData.description}
						placeholder="Enter description (optional)"
						rows="3"
					/>
				</div>
			</div>

			<div class="flex justify-end gap-3">
				<Button type="button" color="light" on:click={closeEditModal} disabled={saving}>
					Cancel
				</Button>
				<Button type="submit" color="blue" disabled={saving}>
					{#if saving}
						<div class="flex items-center gap-2">
							<div
								class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
							></div>
							<span>Saving...</span>
						</div>
					{:else}
						Save Changes
					{/if}
				</Button>
			</div>
		</form>
	</div>
</Modal>

{#if showSuccessToast}
	<div class="fixed bottom-4 right-4">
		<Toast simple color="green">
			<span class="font-semibold">Success!</span>
			<p>Time tracking has been updated successfully.</p>
		</Toast>
	</div>
{/if}

{#if showRouteSuccessToast}
	<div class="fixed bottom-4 right-4">
		<Toast simple color="blue">
			<span class="font-semibold">Success!</span>
			<p>Route information has been updated successfully.</p>
		</Toast>
	</div>
{/if}

<style>
	:global(input:focus, textarea:focus) {
		border-color: #48a851;
		box-shadow: 0 0 0 1px #48a851;
	}
</style>
