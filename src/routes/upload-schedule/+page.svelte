<script lang="ts">
	import { onMount } from 'svelte';
	import { read, utils } from 'xlsx';
	import Dropzone from 'svelte-file-dropzone';
	import { toast } from 'svelte-sonner';
	import { keycloakClient } from '$lib/stores';
	import { get } from 'svelte/store';
	import { environment } from '$lib/environment';
	import { createRide } from '$lib/services/rides.service';
	import { fetchUsers, findPilotByEmailOrName } from '$lib/services/users.service';
	import { fetchRideStatuses } from '$lib/services/ride-status.service';

	let files: File[] = [];
	let workbook: any = null;
	let sheetNames: string[] = [];
	let selectedSheet = '';
	let parsedData: any[] = [];
	let validRows: any[] = [];
	let isLoading = false;
	let isUploading = false;
	let isValidating = false;
	let gliders: any[] = [];
	let locations: any[] = [];
	let pilots: any[] = [];
	let rideStatuses: any[] = [];
	let isApiError = false;
	let apiErrorDetails = '';

	let locationsMapById = new Map();
	let locationsMapByName = new Map();
	let glidersMapById = new Map();
	let glidersMapByName = new Map();
	let pilotsMapById = new Map();

	let rowSelections: { [index: number]: boolean } = {};
	let selectAll = false;

	// Reactive variable for selected count
	$: selectedCount = Object.values(rowSelections).filter(Boolean).length;
	let showConfirmModal = false;
	let uploadSuccess = false;
	let uploadStats = { total: 0, success: 0, failed: 0 };
	let lastError = '';
	let showErrorModal = false;
	let showErrorDetails = false;

	function getSelectedCount() {
		return Object.values(rowSelections).filter(Boolean).length;
	}
	$: if (validRows.length > 0) {
		selectAll = selectedCount === validRows.length;
	}

	onMount(async () => {
		try {
			await Promise.all([
				fetchGliders(),
				fetchLocations(),
				fetchPilots(),
				fetchRideStatuses().then((data) => {
					rideStatuses = data;
				})
			]);
			isApiError = false;
		} catch (error) {
			isApiError = true;
			apiErrorDetails = error.message || 'Failed to load reference data';
			toast.error('Failed to load reference data. Please check network connection and try again.');
		}
	});

	async function fetchGliders() {
		const keycloak = get(keycloakClient);
		const token = keycloak?.token;
		if (!token) {
			throw new Error('Authentication token not available');
		}

		try {
			const response = await fetch(`${environment.gliderMsBackendUrl}/gliders`, {
				headers: {
					Authorization: `Bearer ${token}`,
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				throw new Error(`Failed to load gliders list: ${response.status} ${response.statusText}`);
			}

			gliders = await response.json();
			glidersMapById = new Map(gliders.map((g) => [g.id, g]));
			glidersMapByName = new Map(gliders.map((g) => [g.name, g]));
		} catch (error) {
			throw error;
		}
	}

	async function fetchLocations() {
		const keycloak = get(keycloakClient);
		const token = keycloak?.token;
		if (!token) {
			throw new Error('Authentication token not available');
		}

		try {
			const response = await fetch(`${environment.gliderMsBackendUrl}/locations`, {
				headers: {
					Authorization: `Bearer ${token}`,
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				throw new Error(`Failed to load locations list: ${response.status} ${response.statusText}`);
			}

			locations = await response.json();
			locationsMapById = new Map(locations.map((l) => [l.id, l]));
			locationsMapByName = new Map(locations.map((l) => [l.name, l]));
		} catch (error) {
			throw error;
		}
	}

	async function fetchPilots() {
		try {
			pilots = await fetchUsers();
			pilotsMapById = new Map(pilots.map((p) => [p.id, p]));
			return pilots;
		} catch (error) {
			toast.error('Failed to load pilots data');
			throw error;
		}
	}

	function handleFilesSelect(e: any) {
		const { acceptedFiles } = e.detail;
		files = acceptedFiles;

		if (files.length > 0) {
			readExcelFile(files[0]);
		}
	}

	async function readExcelFile(file: File) {
		isLoading = true;
		try {
			const data = await file.arrayBuffer();
			workbook = read(data);
			sheetNames = workbook.SheetNames;
			selectedSheet = sheetNames[0] || '';

			if (selectedSheet) {
				parseSheet();
			}
		} catch (error) {
			toast.error('Error reading Excel file');
		} finally {
			isLoading = false;
		}
	}

	function parseSheet() {
		if (!workbook || !selectedSheet) return;

		try {
			const sheet = workbook.Sheets[selectedSheet];
			let allParsedData = utils.sheet_to_json(sheet, { raw: true, defval: null });

			parsedData = allParsedData.filter((row) => {
				const nonEmptyFieldsCount = Object.keys(row).filter(
					(key) => row[key] !== undefined && row[key] !== null && row[key] !== ''
				).length;

				return nonEmptyFieldsCount >= 3;
			});

			const isTestLogFile =
				parsedData.length > 0 &&
				(parsedData[0]['Date'] !== undefined ||
					parsedData[0]['Plug Battery @'] !== undefined ||
					parsedData[0]['pilot_id'] !== undefined ||
					parsedData[0]['Ground Crew'] !== undefined);

			parsedData = parsedData.map((row, index) => {
				const normalizedRow = {};

				if (isTestLogFile) {
					if (row['start_location_name'] !== undefined)
						normalizedRow.start_location_name = row['start_location_name'];
					if (row['end_location_name'] !== undefined)
						normalizedRow.end_location_name = row['end_location_name'];
					if (row['Date'] !== undefined) normalizedRow.date = row['Date'];
					if (row['scheduled_start_time'] !== undefined)
						normalizedRow.scheduled_start_time = row['scheduled_start_time'];
					if (row['glider_name'] !== undefined) normalizedRow.glider_name = row['glider_name'];

					if (row['pilot_id'] !== undefined) {
						const pilotValue = row['pilot_id'];
						if (typeof pilotValue === 'string' && pilotValue.includes('@')) {
							normalizedRow.pilot_email = pilotValue;
						} else {
							normalizedRow.pilot_id = pilotValue;
						}
					}

					if (!normalizedRow.pilot_email) {
						Object.keys(row).forEach((key) => {
							const value = row[key];
							const pilotRelatedFields = ['pilot_id', 'pilot_email', 'pilot', 'operator'];
							if (
								pilotRelatedFields.includes(key.toLowerCase()) &&
								typeof value === 'string' &&
								value.includes('@jedsy.com')
							) {
								normalizedRow.pilot_email = value;
							}
						});
					}

					if (row['notes'] !== undefined) normalizedRow.notes = row['notes'];
					if (row['duration'] !== undefined) normalizedRow.duration = row['duration'];
					if (row['scheduled_end_time'] !== undefined)
						normalizedRow.scheduled_end_time = row['scheduled_end_time'];

					return normalizedRow;
				}

				Object.keys(row).forEach((key) => {
					normalizedRow[key.toLowerCase()] = row[key];
				});

				const pilotEmailFields = [
					'pilot_email',
					'pilot email',
					'email',
					'operator',
					'operator_email',
					'pilot'
				];
				const pilotNameFields = ['pilot_name', 'pilot name', 'pilot_full_name', 'operator_name'];
				const pilotIdFields = ['pilot_id', 'pilot id', 'operator_id'];

				for (const field of pilotEmailFields) {
					if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
						const fieldValue = normalizedRow[field].trim();
						if (fieldValue.includes('@')) {
							const emails = fieldValue
								.split(/[/,;]/)
								.map((email) => email.trim())
								.filter((email) => email.includes('@'));
							if (emails.length > 0) {
								normalizedRow.pilot_email = emails[0]; // Take the first valid email
								break;
							}
						} else if (fieldValue) {
							normalizedRow.pilot_name = fieldValue;
						}
					}
				}

				for (const field of pilotNameFields) {
					if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
						normalizedRow.pilot_name = normalizedRow[field];
						break;
					}
				}

				for (const field of pilotIdFields) {
					if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
						const fieldValue = normalizedRow[field].trim();
						if (fieldValue.includes('@')) {
							const emails = fieldValue
								.split(/[/,;]/)
								.map((email) => email.trim())
								.filter((email) => email.includes('@'));
							if (emails.length > 0) {
								normalizedRow.pilot_email = emails[0]; // Take the first valid email
							}
						} else {
							normalizedRow.pilot_id = fieldValue;
						}
						break;
					}
				}

				return normalizedRow;
			});

			propagateDateToEmptyRows();
			validateRows();
		} catch (error) {
			toast.error('Error parsing Excel sheet');
		}
	}

	function propagateDateToEmptyRows() {
		if (parsedData.length === 0) return;

		let firstRowWithDate = null;
		let commonDate = null;

		for (let i = 0; i < parsedData.length; i++) {
			const row = parsedData[i];
			if (row.date !== undefined && row.date !== null) {
				firstRowWithDate = i;
				commonDate = row.date;
				break;
			}

			if (row.Date !== undefined && row.Date !== null) {
				firstRowWithDate = i;
				commonDate = row.Date;
				row.date = row.Date;
				break;
			}
		}

		if (firstRowWithDate !== null && commonDate) {
			for (let i = 0; i < parsedData.length; i++) {
				if (i !== firstRowWithDate && !parsedData[i].date) {
					parsedData[i].date = commonDate;
				}
			}
		}

		for (let i = 0; i < parsedData.length; i++) {
			const row = parsedData[i];

			if (!row.scheduled_start_time) {
				const timeFields = ['time', 'start_time', 'scheduled_start', 'departure'];

				for (const field of timeFields) {
					if (row[field] && typeof row[field] === 'string' && /^\d{1,2}:\d{2}$/.test(row[field])) {
						const [hours, minutes] = row[field].split(':').map(Number);
						const excelTime = hours / 24 + minutes / (24 * 60);
						row.scheduled_start_time = excelTime;
						break;
					}
				}

				if (!row.scheduled_start_time && typeof row['scheduled_start_time'] === 'number') {
					row.scheduled_start_time = row['scheduled_start_time'];
				}
			}
		}
	}

	function excelDateToJSDate(excelDate) {
		if (typeof excelDate === 'number') {
			const millisecondsPerDay = 24 * 60 * 60 * 1000;
			return new Date((excelDate - 25569) * millisecondsPerDay);
		}
		return null;
	}

	function excelTimeToHHMM(excelTime) {
		if (typeof excelTime === 'number') {
			const totalMinutes = Math.round(excelTime * 24 * 60);
			const hours = Math.floor(totalMinutes / 60);
			const minutes = totalMinutes % 60;
			return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
		}
		return excelTime;
	}

	function combineDateAndTime(dateValue, timeValue) {
		let dateObj = null;

		if (typeof dateValue === 'number') {
			dateObj = excelDateToJSDate(dateValue);
		} else if (dateValue) {
			try {
				dateObj = new Date(dateValue);
			} catch (e) {
				return null;
			}
		}

		if (!dateObj || isNaN(dateObj.getTime())) {
			return null;
		}

		if (typeof timeValue === 'number') {
			const hours = Math.floor(timeValue * 24);
			const minutes = Math.floor((timeValue * 24 * 60) % 60);

			dateObj.setHours(hours, minutes, 0, 0);
		} else if (timeValue && typeof timeValue === 'string') {
			const [hours, minutes] = timeValue.split(':').map(Number);
			if (!isNaN(hours) && !isNaN(minutes)) {
				dateObj.setHours(hours, minutes, 0);
			}
		}

		return dateObj;
	}

	function validateRows() {
		isValidating = true;
		try {
			if (isApiError || (gliders.length === 0 && locations.length === 0 && pilots.length === 0)) {
				toast.error('Cannot validate rows: reference data not available');
				validRows = [];
				return;
			}

			validRows = parsedData.map((row, index) => {
				const validatedRow = { ...row, _validationErrors: [] };

				const startLocation =
					locationsMapByName.get(row.start_location_name) ||
					locationsMapById.get(row.start_location_id);
				const endLocation =
					locationsMapByName.get(row.end_location_name) ||
					locationsMapById.get(row.end_location_id);
				const glider = glidersMapByName.get(row.glider_name) || glidersMapById.get(row.glider_id);
				let pilot = null;
				if (row.pilot_email) {
					pilot = findPilotByEmailOrName(pilots, row.pilot_email);
				}
				if (!pilot && row.pilot_name) {
					pilot = findPilotByEmailOrName(pilots, row.pilot_name);
				}
				if (!pilot && row.pilot_id && !row.pilot_id.includes('@')) {
					pilot = pilotsMapById.get(row.pilot_id);
				}
				if (!startLocation) {
					validatedRow._validationErrors.push('Missing or invalid start location');
				} else {
					validatedRow.start_location_id = startLocation.id;
				}

				if (!endLocation) {
					validatedRow._validationErrors.push('Missing or invalid end location');
				} else {
					validatedRow.end_location_id = endLocation.id;
				}

				if (!glider) {
					validatedRow._validationErrors.push('Missing or invalid glider');
				} else {
					validatedRow.glider_id = glider.id;
				}

				if (pilot) {
					validatedRow.pilot_id = pilot.id;
				} else if (row.pilot_email || row.pilot_name || row.pilot_id) {
					const pilotInfo = row.pilot_email || row.pilot_name || row.pilot_id;
					validatedRow._validationErrors.push(
						`Pilot not found: ${pilotInfo}. Please check if the email exists in the system.`
					);
				} else {
					validatedRow._validationErrors.push('Missing pilot information');
				}

				try {
					let processedDate = null;
					let processedTime = null;

					if (typeof row.date === 'number') {
						processedDate = excelDateToJSDate(row.date);
					} else if (row.date) {
						processedDate = new Date(row.date);
					}

					if (typeof row.scheduled_start_time === 'number') {
						if (processedDate) {
							const hours = Math.floor(row.scheduled_start_time * 24);
							const minutes = Math.floor((row.scheduled_start_time * 24 * 60) % 60);

							const dateObj = new Date(processedDate);
							dateObj.setHours(hours, minutes, 0, 0);
							processedTime = dateObj;
						} else {
							const now = new Date();
							now.setHours(0, 0, 0, 0);
							const millisInDay = 24 * 60 * 60 * 1000;
							processedTime = new Date(now.getTime() + row.scheduled_start_time * millisInDay);
						}
					} else if (row.scheduled_start_time) {
						if (processedDate) {
							processedTime = combineDateAndTime(processedDate, row.scheduled_start_time);
						} else {
							processedTime = new Date(row.scheduled_start_time);
						}
					} else if (processedDate) {
						processedTime = processedDate;
					}

					if (processedTime && !isNaN(processedTime.getTime())) {
						validatedRow.scheduled_start_time = processedTime;
						if (!validatedRow.date) {
							validatedRow.date = processedTime;
						}
					} else {
						validatedRow._validationErrors.push('Invalid date or time format');
					}

					if (row.scheduled_end_time) {
						if (typeof row.scheduled_end_time === 'number' && processedDate) {
							const hours = Math.floor(row.scheduled_end_time * 24);
							const minutes = Math.floor((row.scheduled_end_time * 24 * 60) % 60);

							const endDate = new Date(processedDate);
							endDate.setHours(hours, minutes, 0, 0);
							validatedRow.scheduled_end_time = endDate;
						} else if (
							typeof row.scheduled_end_time === 'string' &&
							validatedRow.scheduled_start_time instanceof Date
						) {
							const [hours, minutes] = row.scheduled_end_time.split(':').map(Number);
							if (!isNaN(hours) && !isNaN(minutes)) {
								const endDate = new Date(validatedRow.scheduled_start_time);
								endDate.setHours(hours, minutes, 0);
								validatedRow.scheduled_end_time = endDate;
							}
						}

						if (
							!(validatedRow.scheduled_end_time instanceof Date) ||
							isNaN(validatedRow.scheduled_end_time.getTime())
						) {
							validatedRow.scheduled_end_time = null;
						}
					}

					if (!validatedRow.notes) {
						validatedRow.notes = validatedRow.description || validatedRow.logs || '';
					}
				} catch (e) {
					validatedRow._validationErrors.push(`Data processing error: ${e.message}`);
				}

				return validatedRow;
			});

			initializeSelection();
		} catch (error) {
			console.error('Validation error:', error);
			toast.error(`Validation error: ${error.message}`);
			validRows = [];
		} finally {
			isValidating = false;
		}
	}

	function initializeSelection() {
		rowSelections = {};
		validRows.forEach((_, index) => {
			rowSelections[index] = false;
		});
		updateSelectAllState();
	}

	function updateSelectAllState() {
		const selectedCount = Object.values(rowSelections).filter(Boolean).length;
		selectAll = selectedCount === validRows.length && validRows.length > 0;
	}

	function toggleSelectAll() {
		selectAll = !selectAll;
		validRows.forEach((_, index) => {
			rowSelections[index] = selectAll;
		});
		rowSelections = { ...rowSelections };
	}

	function isRowSelected(index: number) {
		return !!rowSelections[index];
	}

	function toggleRowSelection(index: number) {
		rowSelections[index] = !rowSelections[index];
		rowSelections = { ...rowSelections };
		updateSelectAllState();
	}

	function getSelectedRows() {
		return Object.entries(rowSelections)
			.filter(([_, selected]) => selected)
			.map(([index, _]) => parseInt(index));
	}

	function confirmUpload() {
		const selectedIndices = getSelectedRows();
		const selectedCount = selectedIndices.length;

		if (selectedCount === 0) {
			toast.error('Please select at least one row to upload');
			return;
		}

		showConfirmModal = true;
	}

	function cancelUpload() {
		showConfirmModal = false;
	}

	async function uploadSchedule(event) {
		if (event) event.preventDefault();
		const selectedIndices = getSelectedRows();
		if (selectedIndices.length === 0) {
			toast.error('No rows selected for upload');
			return;
		}

		showConfirmModal = false;
		isUploading = true;
		lastError = '';
		const keycloak = get(keycloakClient);
		const token = keycloak?.token;

		if (!token) {
			toast.error('Not authenticated');
			isUploading = false;
			return;
		}

		let successCount = 0;
		let failCount = 0;

		const rowsToUpload = selectedIndices.map((index) => validRows[index]);
		const totalRows = rowsToUpload.length;

		for (let i = 0; i < rowsToUpload.length; i++) {
			const row = rowsToUpload[i];
			try {
				let departureTime = row.scheduled_start_time;
				let arrivalTime = row.scheduled_end_time || null;

				if (!(departureTime instanceof Date)) {
					departureTime = new Date(departureTime);
				}

				if (arrivalTime && !(arrivalTime instanceof Date)) {
					arrivalTime = new Date(arrivalTime);
				}

				if (isNaN(departureTime.getTime())) {
					throw new Error('Invalid departure time');
				}

				if (arrivalTime && isNaN(arrivalTime.getTime())) {
					arrivalTime = null;
				}

				const pilotId = row.pilot_id;
				const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

				if (!pilotId || !uuidRegex.test(pilotId)) {
					throw new Error(`Please select a valid Pilot for row ${i + 1}`);
				}

				const pilotExists = pilots.some((p) => p.id === pilotId);
				if (!pilotExists) {
					throw new Error(
						`Selected Pilot ID ${pilotId} does not exist in the system. Please select a valid pilot.`
					);
				}

				let fromLocationId = row.start_location_id;
				let toLocationId = row.end_location_id;
				let gliderId = row.glider_id;

				if (!fromLocationId || !locationsMapById.has(fromLocationId))
					throw new Error(`Invalid start location ID for row ${i + 1}`);
				if (!toLocationId || !locationsMapById.has(toLocationId))
					throw new Error(`Invalid end location ID for row ${i + 1}`);
				if (!gliderId || !glidersMapById.has(gliderId))
					throw new Error(`Invalid glider ID for row ${i + 1}`);

				if (fromLocationId === toLocationId && locationsMapById.size > 1) {
					for (const [id] of locationsMapById.entries()) {
						if (id !== fromLocationId) {
							toLocationId = id;
							break;
						}
					}
				}

				const gliderName = glidersMapById.get(gliderId)?.name || 'Unknown Glider';

				const defaultStatusId = rideStatuses.length > 0 ? rideStatuses[0].id : 1;

				const flightData = {
					from_location: fromLocationId,
					to_location: toLocationId,
					departure_time: departureTime.toISOString(),
					arrival_time: arrivalTime ? arrivalTime.toISOString() : null,
					ride_status_id: defaultStatusId,
					glider_id: gliderId,
					glider_name: gliderName,
					operator_id: pilotId,
					has_package: false,
					package_description: row.notes || '',
					route_id: null,
					cancel_reason: null
				};

				try {
					await createRide(flightData);
					successCount++;
					toast.success(`Uploaded flight ${i + 1}/${totalRows}`);
				} catch (error) {
					failCount++;
					console.error('API Error:', error);
					let errorMessage = error.message || 'Unknown error';

					if (error.message && error.message.includes('Failed to fetch')) {
						errorMessage =
							'Network error: cannot connect to server. Check your internet connection or server availability.';
					} else if (error.response) {
						errorMessage = `Server error (${error.response.status}): ${error.response.data?.message || errorMessage}`;
						console.error('Error response:', error.response);
					}

					lastError = errorMessage;
					toast.error(`Error processing flight ${i + 1}/${totalRows}: ${errorMessage}`, {
						duration: 5000
					});
				}
			} catch (error) {
				failCount++;
				console.error('General error:', error);
				let errorMessage = error.message || 'Unknown error';
				lastError = errorMessage;
				toast.error(`Error processing flight ${i + 1}/${totalRows}: ${errorMessage}`, {
					duration: 5000
				});
			}
		}

		isUploading = false;
		uploadStats = {
			total: totalRows,
			success: successCount,
			failed: failCount
		};

		if (failCount === 0) {
			uploadSuccess = true;
			toast.success(`Successfully uploaded ${successCount} flights`);
		} else {
			toast.error(`Uploaded ${successCount} flights, with errors: ${failCount}`);
		}
	}

	function resetAfterUpload() {
		uploadSuccess = false;

		if (uploadStats.success > 0) {
			const selectedIndices = getSelectedRows();
			selectedIndices.forEach((index) => {
				rowSelections[index] = false;
			});
			rowSelections = { ...rowSelections };
			updateSelectAllState();
		}
	}
</script>

<div class="container mx-auto w-full max-w-full px-2 py-6">
	<h1 class="mb-6 text-2xl font-bold">Upload Flight Schedule</h1>

	{#if lastError}
		<div class="mb-6 rounded border-l-4 border-red-500 bg-red-50 px-4 py-3 text-red-700 shadow-md">
			<div class="flex items-start justify-between">
				<div>
					<h2 class="mb-2 font-bold">Upload Error</h2>
					<p>{lastError}</p>
				</div>
				<button
					class="text-xl font-bold text-red-500 hover:text-red-700"
					on:click={() => (lastError = '')}
				>
					×
				</button>
			</div>
			<button
				class="btn btn-secondary btn-sm mt-3"
				on:click={() => (showErrorDetails = !showErrorDetails)}
			>
				{showErrorDetails ? 'Hide details' : 'Show details'}
			</button>
			{#if showErrorDetails}
				<div class="mt-2 rounded bg-red-100 p-2 text-sm">
					<p class="mb-1">If you see "Failed to fetch" error:</p>
					<ul class="list-disc pl-5">
						<li>Check your internet connection</li>
						<li>Check VPN if using</li>
						<li>API server might be unavailable - contact administrator</li>
					</ul>
				</div>
			{/if}
		</div>
	{/if}

	{#if isApiError}
		<div class="mb-6 rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
			<h2 class="mb-2 font-bold">API Connection Error</h2>
			<p>
				Unable to fetch reference data needed for validation. Please check network connection and
				try again.
			</p>
			{#if apiErrorDetails}
				<div class="mt-2 rounded bg-red-100 p-2 text-sm">
					<strong>Error details:</strong>
					{apiErrorDetails}
				</div>
			{/if}
			<button class="btn btn-primary btn-md mt-4" on:click={() => window.location.reload()}>
				Reload Page
			</button>
		</div>
	{/if}

	{#if uploadSuccess}
		<div class="mb-6 rounded border border-green-200 bg-green-50 px-4 py-3 text-green-700">
			<h2 class="mb-2 font-bold">Upload Successful</h2>
			<p>Successfully uploaded {uploadStats.success} flights to the system.</p>

			<button class="btn btn-primary btn-md mt-4" on:click={resetAfterUpload}>
				Upload Another File
			</button>
		</div>
	{:else}
		<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
			<h2 class="mb-4 text-xl font-semibold">1. Upload Excel File</h2>

			{#if files.length === 0}
				<Dropzone
					accept=".xlsx,.xls"
					on:drop={handleFilesSelect}
					class="cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-10 text-center hover:bg-gray-50"
				>
					<p class="text-gray-500">Drag and drop Excel file here or click to select</p>
				</Dropzone>
			{:else}
				<div
					class="flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4"
				>
					<div>
						<p class="font-medium">{files[0].name}</p>
						<p class="text-sm text-gray-500">{(files[0].size / 1024).toFixed(2)} KB</p>
					</div>
					<button
						class="btn btn-text text-red-500 hover:text-red-700"
						on:click={() => {
							files = [];
							workbook = null;
							sheetNames = [];
							selectedSheet = '';
							parsedData = [];
							validRows = [];
							rowSelections = {};
							selectAll = false;
						}}
					>
						Remove
					</button>
				</div>
			{/if}
		</div>

		{#if sheetNames.length > 0}
			<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">2. Select Sheet to Analyze</h2>

				<div class="mb-4">
					<label for="sheetSelect" class="mb-1 block text-sm font-medium text-gray-700"
						>Select sheet:</label
					>
					<select
						id="sheetSelect"
						bind:value={selectedSheet}
						on:change={parseSheet}
						class="focus:ring-primary focus:border-primary mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none sm:text-sm"
					>
						{#each sheetNames as name}
							<option value={name}>{name}</option>
						{/each}
					</select>
				</div>
			</div>
		{/if}

		{#if isLoading || isValidating}
			<div class="my-8 flex justify-center">
				<div class="spinner-container-center">
					<div class="common-spinner spinner-lg"></div>
					<span class="ml-2 text-gray-500"
						>{isLoading ? 'Reading file...' : 'Validating data...'}</span
					>
				</div>
			</div>
		{:else if validRows.length > 0}
			<div class="mb-6 rounded-lg bg-white p-4 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">3. Verify and Confirm Data</h2>

				<div class="mb-4 flex items-center justify-between">
					<p>Found {validRows.length} rows ready for upload out of {parsedData.length} total.</p>

					<div class="flex items-center">
						<label class="mr-4 inline-flex items-center">
							<input
								type="checkbox"
								checked={selectAll}
								on:change={toggleSelectAll}
								class="text-primary focus:border-primary focus:ring-primary rounded border-gray-300 focus:ring focus:ring-opacity-50"
							/>
							<span class="ml-2 text-sm">Select All</span>
						</label>

						<span class="text-sm text-gray-600"
							>{getSelectedCount()} of {validRows.length} selected</span
						>
					</div>
				</div>

				<div class="w-full overflow-x-auto">
					<table class="min-w-full table-fixed divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th
									scope="col"
									class="w-10 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
								>
									Select
								</th>
								<th
									scope="col"
									class="w-8 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
								>
									#
								</th>
								<th
									scope="col"
									class="w-28 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Glider</th
								>
								<th
									scope="col"
									class="w-32 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>From</th
								>
								<th
									scope="col"
									class="w-32 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>To</th
								>
								<th
									scope="col"
									class="w-48 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Pilot</th
								>
								<th
									scope="col"
									class="w-24 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Date</th
								>
								<th
									scope="col"
									class="w-20 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Departure</th
								>
								<th
									scope="col"
									class="w-20 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Arrival</th
								>
								<th
									scope="col"
									class="w-24 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Notes</th
								>
							</tr>
						</thead>
						<tbody class="divide-y divide-gray-200 bg-white">
							{#each validRows as row, i}
								<tr
									class={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
									class:bg-blue-50={isRowSelected(i)}
								>
									<td class="whitespace-nowrap px-2 py-2">
										<input
											type="checkbox"
											checked={isRowSelected(i)}
											on:change={() => toggleRowSelection(i)}
											class="text-primary focus:border-primary focus:ring-primary cursor-pointer rounded border-gray-300 focus:ring focus:ring-opacity-50"
										/>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500">
										{row.sort || i + 1}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm font-medium text-gray-900">
										<select
											bind:value={row.glider_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border border-gray-300 px-1 py-1 text-sm"
										>
											<option value="">Select Glider</option>
											{#each gliders as glider}
												<option value={glider.id}>{glider.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.start_location_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border border-gray-300 px-1 py-1 text-sm"
										>
											<option value="">Select Location</option>
											{#each locations as location}
												<option value={location.id}>{location.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.end_location_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border border-gray-300 px-1 py-1 text-sm"
										>
											<option value="">Select Location</option>
											{#each locations as location}
												<option value={location.id}>{location.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.pilot_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border border-gray-300 px-1 py-1 text-sm"
										>
											<option value="">Select Pilot</option>
											{#each pilots as pilot}
												<option value={pilot.id}
													>{pilot.email || `${pilot.firstName} ${pilot.lastName}`}</option
												>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										{row.scheduled_start_time instanceof Date
											? row.scheduled_start_time.toLocaleDateString()
											: new Date(excelDateToJSDate(row.date)).toLocaleDateString()}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										{row.scheduled_start_time instanceof Date
											? row.scheduled_start_time.toLocaleTimeString([], {
													hour: '2-digit',
													minute: '2-digit'
												})
											: typeof row.scheduled_start_time === 'number'
												? excelTimeToHHMM(row.scheduled_start_time)
												: row.scheduled_start_time}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										{row.scheduled_end_time
											? row.scheduled_end_time instanceof Date
												? row.scheduled_end_time.toLocaleTimeString([], {
														hour: '2-digit',
														minute: '2-digit'
													})
												: typeof row.scheduled_end_time === 'number'
													? excelTimeToHHMM(row.scheduled_end_time)
													: row.scheduled_end_time
											: '-'}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										{row.notes || '-'}
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>

				<div class="mt-6 flex flex-col items-center justify-between sm:flex-row">
					{#if selectedCount === 0}
						<p class="mb-2 text-red-500 sm:mb-0">Select rows to upload</p>
					{:else}
						<div class="mb-2 flex items-center sm:mb-0">
							<p class="mr-3 text-green-600">
								Selected {selectedCount} of {validRows.length} rows
							</p>
							<button
								class="btn btn-secondary btn-sm ml-2"
								on:click={() => {
									rowSelections = {};
									updateSelectAllState();
								}}
							>
								Clear Selection
							</button>
						</div>
					{/if}

					<button
						class="btn btn-primary btn-md mt-2 sm:mt-0 {selectedCount === 0 ? 'opacity-60' : ''}"
						on:click={(e) => confirmUpload()}
						disabled={selectedCount === 0}
					>
						Upload Selected Rows
					</button>
				</div>
			</div>
		{:else if parsedData.length > 0}
			<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">Validation Results</h2>

				<div class="mb-4 mt-6">
					<p class="mb-2 font-medium text-gray-700">All found rows ({parsedData.length}):</p>
					<div class="w-full overflow-x-auto rounded-lg border border-gray-200">
						<table class="min-w-full table-fixed text-sm">
							<thead>
								<tr class="bg-gray-100">
									<th class="w-8 border-b border-r border-gray-200 px-2 py-2 text-left">№</th>
									<th class="w-10 border-b border-r border-gray-200 px-2 py-2 text-left">Select</th>
									<th class="w-32 border-b border-r border-gray-200 px-2 py-2 text-left"
										>start_location</th
									>
									<th class="w-32 border-b border-r border-gray-200 px-2 py-2 text-left"
										>end_location</th
									>
									<th class="w-24 border-b border-r border-gray-200 px-2 py-2 text-left">date</th>
									<th class="w-20 border-b border-r border-gray-200 px-2 py-2 text-left">time</th>
									<th class="w-28 border-b border-r border-gray-200 px-2 py-2 text-left">glider</th>
									<th class="w-48 border-b border-r border-gray-200 px-2 py-2 text-left">pilot</th>
									<th class="w-24 border-b border-gray-200 px-2 py-2 text-left">notes</th>
								</tr>
							</thead>
							<tbody>
								{#each validRows as row, i}
									<tr class={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
										<td class="border-r border-gray-200 px-2 py-2">{row.sort || i + 1}</td>
										<td class="border-r border-gray-200 px-2 py-2">
											<input
												type="checkbox"
												checked={isRowSelected(i)}
												on:change={() => toggleRowSelection(i)}
												class="text-primary focus:border-primary focus:ring-primary cursor-pointer rounded border-gray-300 focus:ring focus:ring-opacity-50"
											/>
										</td>
										<td class="border-r border-gray-200 px-2 py-2"
											>{row.start_location_name || ''}</td
										>
										<td class="border-r border-gray-200 px-2 py-2">{row.end_location_name || ''}</td
										>
										<td class="border-r border-gray-200 px-2 py-2">
											{#if row.date}
												{typeof row.date === 'number'
													? excelDateToJSDate(row.date)?.toLocaleDateString()
													: new Date(row.date).toLocaleDateString()}
											{/if}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">
											{#if row.scheduled_start_time}
												{typeof row.scheduled_start_time === 'number'
													? excelTimeToHHMM(row.scheduled_start_time)
													: row.scheduled_start_time instanceof Date
														? row.scheduled_start_time.toLocaleTimeString([], {
																hour: '2-digit',
																minute: '2-digit'
															})
														: row.scheduled_start_time}
											{/if}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">{row.glider_name || ''}</td>
										<td class="border-r border-gray-200 px-2 py-2">
											{row.pilot_name || row.pilot_id || row.pilot_email || ''}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">{row.notes || ''}</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>

				<div class="mt-4 flex flex-col items-center justify-between sm:flex-row">
					<div class="mb-4 sm:mb-0">
						<label class="mr-4 inline-flex items-center">
							<input
								type="checkbox"
								checked={selectAll}
								on:change={toggleSelectAll}
								class="text-primary focus:border-primary focus:ring-primary rounded border-gray-300 focus:ring focus:ring-opacity-50"
							/>
							<span class="ml-2 text-sm">Select All</span>
						</label>
						<span class="text-sm text-gray-600"
							>{getSelectedCount()} of {validRows.length} selected</span
						>

						<button
							class="btn btn-secondary btn-sm ml-3"
							on:click={() => {
								rowSelections = {};
								updateSelectAllState();
							}}
							disabled={getSelectedCount() === 0}
						>
							Clear Selection
						</button>
					</div>

					<button
						class="btn btn-primary btn-md"
						on:click={(e) => confirmUpload()}
						disabled={selectedCount === 0}
					>
						Upload Selected Rows
					</button>
				</div>

				<p class="mt-4 text-gray-700">
					For uploading, the file must contain the following columns (case insensitive):
				</p>
				<ul class="mb-4 mt-2 list-disc pl-5 text-gray-600">
					<li>glider_id or glider_name</li>
					<li>start_location_name (or simply "start_location")</li>
					<li>end_location_name (or simply "end_location")</li>
					<li>pilot_id or pilot_name (email addresses also supported)</li>
					<li>scheduled_start_time (or "date" column if time is included)</li>
					<li>scheduled_end_time (optional)</li>
					<li>notes, logs, or description (optional, used as package_description)</li>
				</ul>

				<div class="mt-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
					<h3 class="mb-2 font-medium text-yellow-800">Diagnostic Information:</h3>
					<ul class="list-disc pl-5 text-yellow-700">
						<li>Gliders loaded: {gliders.length}</li>
						<li>Locations loaded: {locations.length}</li>
						<li>Pilots loaded: {pilots.length}</li>
						<li>Rows parsed: {parsedData.length}</li>
						<li>
							Valid rows: {validRows.filter(
								(r) => !r._validationErrors || r._validationErrors.length === 0
							).length}
						</li>
					</ul>
					<p class="mt-2 text-sm text-yellow-700">
						For troubleshooting, open developer console (F12) to view detailed information.
					</p>
				</div>
			</div>
		{/if}
	{/if}
</div>

{#if showConfirmModal}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
		<div class="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
			<h3 class="mb-4 text-lg font-bold">Confirm Upload</h3>
			<p class="mb-4">
				Are you sure you want to upload {getSelectedCount()} flights to the system?
			</p>

			<div class="flex justify-end space-x-3">
				<button class="btn btn-secondary btn-md" on:click={cancelUpload}> Cancel </button>

				<button class="btn btn-primary btn-md" on:click={(e) => uploadSchedule(e)}>
					Confirm Upload
				</button>
			</div>
		</div>
	</div>
{/if}

{#if isUploading}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
		<div class="rounded-lg bg-white p-6 text-center shadow-xl">
			<div class="spinner-container-center">
				<div class="common-spinner spinner-lg"></div>
			</div>
			<h3 class="mb-2 text-lg font-bold">Uploading...</h3>
			<p>Please wait while your flights are being uploaded.</p>
		</div>
	</div>
{/if}

<style>
	.btn {
		@apply inline-flex cursor-pointer items-center justify-center border-0 text-center font-medium;
	}

	.btn-primary {
		@apply bg-primary-500 text-white transition-all duration-300;
	}

	.btn-primary:hover:not(:disabled) {
		@apply -translate-y-0.5 transform bg-primary-600 shadow-md;
	}

	.btn-primary:disabled {
		@apply cursor-not-allowed opacity-60;
	}

	.btn-secondary {
		@apply border border-gray-200 bg-gray-100 text-gray-600 transition-all duration-300;
	}

	.btn-secondary:hover:not(:disabled) {
		@apply bg-gray-200 text-gray-800;
	}

	.btn-text {
		@apply border-0 bg-transparent p-0 text-primary-500 transition-all duration-150;
	}

	.btn-text:hover {
		@apply text-primary-600 underline;
	}

	.btn-md {
		@apply gap-2 rounded-md px-4 py-2 text-base;
	}

	.btn-sm {
		@apply gap-1 rounded px-2 py-1 text-xs;
	}

	.common-spinner {
		@apply inline-block animate-spin rounded-full;
		border: 3px solid rgba(72, 168, 81, 0.3);
		border-top-color: theme('colors.primary.500');
	}

	.spinner-lg {
		@apply h-8 w-8;
	}

	.spinner-container-center {
		@apply flex items-center justify-center p-4;
	}
</style>
