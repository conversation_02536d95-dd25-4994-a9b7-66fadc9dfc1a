<script lang="ts">
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import Keycloak from 'keycloak-js';
	import '../styles/app.css';
	import Sidebar from '../lib/components/layout/Sidebar.svelte';
	import Header from '$components/layout/Header.svelte';
	import { userProfile, keycloakClient } from '$lib/stores';
	import { QueryClient, QueryClientProvider } from '@sveltestack/svelte-query';
	import { environment } from '$lib/environment';
	import { UserProfileService } from '$lib/services/user-profile.service';
	import type { KeycloakInitOptions } from 'keycloak-js';
	import { Toaster } from 'svelte-sonner';

	const sidebarOpen = writable(true);

	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				staleTime: 1000 * 60 * 5,
				refetchOnWindowFocus: false
			}
		}
	});
	let keycloak_initialized = false;

	onMount(async () => {
		if (import.meta.env.VITE_BYPASS_AUTH === 'true') {
			keycloak_initialized = true;
			return;
		}
		const instance = {
			url: environment.authUrl,
			realm: environment.authRealm,
			clientId: environment.authClientId
		};

		const keycloak = new Keycloak(instance);
		keycloakClient.set(keycloak);

		const token = localStorage.getItem('kc_token');
		const refreshToken = localStorage.getItem('kc_refreshToken');

		const initOptions: KeycloakInitOptions = {
			onLoad: 'login-required',
			checkLoginIframe: false,
			token,
			refreshToken,
			redirectUri: window.location.origin + '/'
		};

		try {
			const authenticated = await keycloak.init(initOptions);

			if (authenticated) {
				localStorage.setItem('kc_token', keycloak.token ?? '');
				localStorage.setItem('kc_refreshToken', keycloak.refreshToken ?? '');

				const profile = await keycloak.loadUserProfile();
				userProfile.set(profile);
				UserProfileService.storeUserDataInLocalStorage(profile);

				keycloak_initialized = true;
			}
		} catch (error) {
			console.error('Keycloak initialization failed:', error);
		}

		keycloak.onTokenExpired = async () => {
			await keycloak.updateToken(30);
		};
	});
</script>

<QueryClientProvider client={queryClient}>
	<div class="flex min-h-screen flex-col bg-gray-100">
		<Header />
		<div class="relative flex flex-1">
			<!-- Sidebar -->
			<aside
				class="border-r bg-white shadow transition-all duration-300 ease-in-out {$sidebarOpen
					? 'w-72'
					: 'w-0'}"
				style="min-width: {$sidebarOpen ? '288px' : '0'};"
			>
				<div class="h-full {$sidebarOpen ? 'block' : 'hidden'}">
					<Sidebar />
				</div>
			</aside>

			<button
				on:click={() => sidebarOpen.update((open) => !open)}
				class="absolute top-20 {$sidebarOpen
					? 'left-72'
					: 'left-4'} z-50 flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 bg-white text-gray-700 shadow-lg transition-all duration-300 ease-in-out hover:bg-gray-50 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
				aria-label={$sidebarOpen ? 'Close sidebar' : 'Open sidebar'}
				title={$sidebarOpen ? 'Close sidebar' : 'Open sidebar'}
			>
				<svg
					class="h-5 w-5 transition-transform duration-300 {$sidebarOpen ? '' : 'rotate-180'}"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					stroke-width="2"
				>
					<path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
				</svg>
			</button>

			<main class="flex-1 overflow-auto p-4 transition-all duration-300 ease-in-out">
				{#if keycloak_initialized}
					<slot />
				{:else}
					<div class="flex h-screen items-center justify-center">
						<div
							class="border-primary h-32 w-32 animate-spin rounded-full border-b-2 border-t-2"
						></div>
					</div>
				{/if}
			</main>
		</div>
	</div>
</QueryClientProvider>

<Toaster richColors position="top-right" />
