<script lang="ts">
	import { Card } from 'flowbite-svelte';
	import {
		DownloadSolid,
		FilterSolid,
		FileSolid,
		CloudArrowUpOutline,
		MapPinOutline,
		TruckOutline,
		CogOutline,
		CalendarWeekOutline
	} from 'flowbite-svelte-icons';
	import { userProfile } from '$lib/stores';
	import { environment } from '$lib/environment';

	const quickNavItems = [
		{
			title: 'Software Versions',
			href: '/software',
			icon: DownloadSolid,
			color: 'bg-blue-500',
			internal: true
		},
		{
			title: 'Routes & Time Tracking',
			href: '/routes',
			icon: FilterSolid,
			color: 'bg-green-500',
			internal: true
		},
		{
			title: 'Time Tracking History',
			href: '/shifts',
			icon: FileSolid,
			color: 'bg-orange-500',
			internal: true
		},
		{
			title: 'Upload Flight Schedule',
			href: '/upload-schedule',
			icon: CloudArrowUpOutline,
			color: 'bg-indigo-500',
			internal: true
		},
		{
			title: 'Locations',
			href: `${environment.urlMainApp}/locations`,
			icon: MapPinOutline,
			color: 'bg-red-500',
			internal: false
		},
		{
			title: 'Fleet Management',
			href: `${environment.urlMainApp}/fleets`,
			icon: TruckOutline,
			color: 'bg-yellow-500',
			internal: false
		},
		{
			title: 'Equipment Maintenance',
			href: '/maintenance',
			icon: CogOutline,
			color: 'bg-purple-500',
			internal: true
		},
		{
			title: 'Scheduled Rides',
			href: '/rides',
			icon: CalendarWeekOutline,
			color: 'bg-teal-500',
			internal: true
		}
	];

	function handleNavigation(href: string, internal: boolean = false) {
		if (internal) {
			window.location.href = href;
		} else {
			window.open(href, '_blank');
		}
	}
</script>

<div class="min-h-screen py-8">
	<div class="w-full px-6">
		<!-- Welcome Header -->
		<div class="mb-12 text-center">
			<h1 class="mb-4 text-4xl font-bold text-gray-900">Welcome to Jedsy Admin Portal</h1>
			{#if $userProfile}
				<p class="text-lg text-primary-600">
					Hello, {$userProfile.email || 'Admin'}! 👋
				</p>
			{/if}
		</div>

		<!-- Main Features Section -->
		<div class="mb-12">
			<h2 class="mb-6 text-center text-2xl font-bold text-gray-900">Core Management Features</h2>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
				{#each quickNavItems as item}
					<Card
						class="group cursor-pointer transition-all duration-300 hover:shadow-lg"
						on:click={() => handleNavigation(item.href, item.internal)}
					>
						<div class="p-6 text-center">
							<div
								class="h-16 w-16 {item.color} mx-auto mb-4 flex items-center justify-center rounded-full transition-transform group-hover:scale-110"
							>
								<svelte:component this={item.icon} class="h-8 w-8 text-white" />
							</div>
							<h3
								class="mb-2 text-lg font-semibold text-gray-900 transition-colors group-hover:text-primary-600"
							>
								{item.title}
							</h3>
						</div>
					</Card>
				{/each}
			</div>
		</div>
	</div>
</div>
