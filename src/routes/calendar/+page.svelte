<!--<script>-->
<!--	import Calendar from '@event-calendar/core';-->
<!--	import ResourceTimeGrid from '@event-calendar/resource-time-grid';-->
<!--	import Interaction from '@event-calendar/interaction';-->

<!--	let resources = [-->
<!--		{ id: 1, title: 'PILOT A' },-->
<!--		{ id: 2, title: 'PILOT B' },-->
<!--		{ id: 3, title: 'PILOT C' },-->
<!--		{ id: 4, title: 'PILOT D' },-->
<!--		{ id: 5, title: 'PILOT E' },-->
<!--		{ id: 6, title: 'PILOT F' }-->
<!--	];-->
<!--	let events = [-->
<!--		{ id: 101, resourceId: 1, title: 'Flight A', start: '2025-02-26T09:00:00', end: '2025-02-26T11:00:00' },-->
<!--		{ id: 102, resourceId: 2, title: 'Flight B', start: '2025-02-26T10:00:00', end: '2025-02-26T12:30:00' }-->
<!--	];-->

<!--	let calendarRef;-->

<!--	let plugins = [ResourceTimeGrid, Interaction];-->

<!--	let options = {-->
<!--		view: 'resourceTimeGridDay',-->
<!--		resources,-->
<!--		eventSources: [-->
<!--			{-->
<!--				events: (info, successCallback) => {-->
<!--					successCallback(events);-->
<!--				}-->
<!--			}-->
<!--		],-->
<!--		editable: true,-->
<!--		eventStartEditable: true,-->
<!--		eventDurationEditable: true,-->
<!--		eventDrop: (info) => {-->
<!--			const { event } = info;-->
<!--			const idx = events.findIndex(e => e.id === event.id);-->
<!--			if (idx !== -1) {-->
<!--				events[idx] = {-->
<!--					...events[idx],-->
<!--					start: event.start,-->
<!--					end: event.end,-->
<!--					resourceId: event.resource ? event.resource.id : events[idx].resourceId-->
<!--				};-->
<!--				calendarRef.refetchEvents();-->
<!--			}-->
<!--		},-->
<!--		eventResize: (info) => {-->
<!--			const { event } = info;-->
<!--			const idx = events.findIndex(e => e.id === event.id);-->
<!--			if (idx !== -1) {-->
<!--				events[idx] = {-->
<!--					...events[idx],-->
<!--					start: event.start,-->
<!--					end: event.end-->
<!--				};-->
<!--				calendarRef.refetchEvents();-->
<!--			}-->
<!--		},-->
<!--		datesAboveResources: true,-->
<!--		slotDuration: '00:30:00',-->
<!--		slotMinTime: '08:00:00',-->
<!--		slotMaxTime: '22:00:00',-->
<!--		headerToolbar: {-->
<!--			start: 'today prev,next',-->
<!--			center: 'title',-->
<!--			end: 'resourceTimeGridDay,resourceTimeGridWeek'-->
<!--		},-->
<!--		dateClick: (info) => {-->
<!--			console.log('Day:', info.dateStr, 'Recource:', info.resource?.id);-->
<!--		},-->
<!--		eventClick: (info) => {-->
<!--			console.log('Click:', info.event.id);-->
<!--		}-->
<!--	};-->
<!--</script>-->

<!--<Calendar bind:this={calendarRef} {plugins} {options} style="height: 800px; width: 100%;" />-->
<script>
	import Calendar from '@event-calendar/core';
	import ResourceTimeline from '@event-calendar/resource-timeline';
	import Interaction from '@event-calendar/interaction';
	import '@event-calendar/core/index.css';
	import { onMount } from 'svelte';
	let resources = [
		{ id: 1, title: 'PILOT A' },
		{ id: 2, title: 'PILOT B' },
		{ id: 3, title: 'PILOT C' },
		{ id: 4, title: 'PILOT D' },
		{ id: 5, title: 'PILOT E' },
		{ id: 6, title: 'PILOT F' }
	];

	let events = [
		{
			id: 101,
			resourceIds: [1],
			title: 'Flight A',
			start: '2025-02-26T09:00:00',
			end: '2025-02-26T11:00:00',
			backgroundColor: '#4caf50',
			textColor: '#fff',
			classNames: ['custom-event'],
			extendedProps: { flightNumber: 'AA123', status: 'On Time' }
		},
		{
			id: 102,
			resourceIds: [2],
			title: 'Flight B',
			start: '2025-02-26T10:00:00',
			end: '2025-02-26T12:30:00',
			backgroundColor: '#f44336',
			textColor: '#fff',
			classNames: ['warning-event'],
			extendedProps: { flightNumber: 'BB456', status: 'Delayed' }
		},
		{
			id: 103,
			resourceIds: [3],
			title: 'Flight C',
			start: '2025-02-26T13:00:00',
			end: '2025-02-26T15:00:00',
			backgroundColor: '#2196f3',
			textColor: '#fff',
			classNames: ['info-event'],
			extendedProps: { flightNumber: 'CC789', status: 'Cancelled' }
		}
	];

	let calendarRef;

	let plugins = [ResourceTimeline, Interaction];

	let options = {
		date: new Date().toISOString().split('T')[0],
		resourceAreaHeaderContent: 'Pilots',
		view: 'resourceTimelineDay',
		resources,
		events,
		editable: true,
		eventDrop: (info) => {
			const { event } = info;
			const idx = events.findIndex((e) => e.id === event.id);
			if (idx !== -1) {
				events[idx] = {
					...events[idx],
					start: event.start,
					end: event.end,
					resourceIds: event.resource ? [event.resource.id] : events[idx].resourceIds
				};
				calendarRef.refetchEvents();
			}
		},
		eventResize: (info) => {
			const { event } = info;
			const idx = events.findIndex((e) => e.id === event.id);
			if (idx !== -1) {
				events[idx] = { ...events[idx], start: event.start, end: event.end };
				calendarRef.refetchEvents();
			}
		},
		headerToolbar: {
			start: 'today prev,next',
			center: 'title',
			end: 'resourceTimelineDay,resourceTimelineWeek'
		},
		slotDuration: '00:30:00',
		slotMinTime: '08:00:00',
		slotMaxTime: '22:00:00',
		datesAboveResources: true,
		dateClick: (info) => {
			console.log('date', info.dateStr, 'Recource:', info.resource?.id);
		},
		eventClick: (info) => {
			console.log('click:', info.event.id);
		}
	};
	function setSidebarTitleWidth() {
		const sidebarTitles = document.querySelectorAll('.ec-sidebar-title');
		const sidebarDayTitles = document.querySelectorAll('.ec-day-head');

		sidebarTitles.forEach((title) => {
			if (title instanceof HTMLElement) {
				title.style.flexBasis = '50px';
			}
		});
		sidebarDayTitles.forEach((title) => {
			if (title instanceof HTMLElement) {
				title.style.flexBasis = '25px';
			}
		});
	}

	onMount(() => {
		requestAnimationFrame(() => {
			if (calendarRef) {
				calendarRef.refetchEvents();
				calendarRef.updateSize();
			}
		});
		setSidebarTitleWidth();
	});
</script>

<Calendar bind:this={calendarRef} {plugins} {options} style="height: 800px; width: 100%;" />
