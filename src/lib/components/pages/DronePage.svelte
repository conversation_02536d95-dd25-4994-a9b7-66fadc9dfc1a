<script lang="ts">
	import { onMount } from 'svelte';
	import { get } from 'svelte/store';
	import { userProfile } from '$lib/stores';
	import {
		<PERSON>ton,
		Card,
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Toast,
		Select
	} from 'flowbite-svelte';
	import { fetchGliders, type Glider } from '$lib/services/gliders.service';
	import { fetchNetworkDevices, type NetworkDevice } from '$lib/services/network.service';
	import { fetchUsers, type UserData } from '$lib/services/users.service';
	import { EditOutline, CheckCircleOutline } from 'flowbite-svelte-icons';
	import { formatDate } from '$lib/utils/datetime';
	import EditDroneModal from '$lib/components/common/modals/EditDroneModal.svelte';
	import { toast } from 'svelte-sonner';

	// Props
	export let droneId: string;

	let drone: Glider | null = null;
	let networkDevices: NetworkDevice[] = [];
	let loading = true;
	let error = '';

	const email = get(userProfile)?.email ?? '';

	let showEditModal = false;
	let showSuccessToast = false;

	let users: UserData[] = [];
	let selectedLaptopUserEmail = email; // Default to current user
	let selectedSmartphoneUserEmail = email; // Default to current user

	async function loadUsers() {
		try {
			users = await fetchUsers();
		} catch (error) {
			const message = error instanceof Error ? error.message : 'Failed to load users';
			toast.error(message);
			users = [];
		}
	}

	async function loadAllNetworkDevices() {
		try {
			const droneName = drone?.name || `${drone?.line}-${drone?.number}`;

			const allDevicesFromApi = await fetchNetworkDevices(null, null);
			const droneDevices = await fetchNetworkDevices(droneName, null);
			const userDevicesPromises = users.map(async (user) => {
				try {
					return await fetchNetworkDevices(null, user.email);
				} catch (error) {
					const message =
						error instanceof Error ? error.message : 'Failed to load devices for user';
					toast.error(message);
					return [];
				}
			});

			const userDevicesArrays = await Promise.all(userDevicesPromises);
			const userDevices = userDevicesArrays.flat();

			const allDevices = [...allDevicesFromApi, ...droneDevices, ...userDevices];
			const uniqueDevices = allDevices.filter(
				(device, index, self) => index === self.findIndex((d) => d.id === device.id)
			);

			networkDevices = uniqueDevices;
		} catch (error) {
			const message = error instanceof Error ? error.message : 'Failed to load network devices';
			toast.error(message);
		}
	}

	async function loadDroneData() {
		try {
			loading = true;
			error = '';
			const gliders = await fetchGliders();
			drone =
				gliders.find((g) => g.name === droneId || `${g.line}-${g.number}` === droneId) || null;
			if (!drone) {
				error = `Drone ${droneId} not found`;
				return;
			}

			await loadUsers();
			await loadAllNetworkDevices();
		} catch {
			error = 'Failed to load drone data';
		} finally {
			loading = false;
		}
	}

	function openEditModal() {
		showEditModal = true;
	}

	function closeEditModal() {
		showEditModal = false;
	}

	async function handleEditSuccess() {
		showSuccessToast = true;
		closeEditModal();
		await loadDroneData();
		setTimeout(() => (showSuccessToast = false), 3000);
	}

	function getDroneDevice(): NetworkDevice | null {
		return (
			networkDevices.find(
				(device) => device.deviceType === 'drone' && device.interfaceRole === 'primary'
			) || null
		);
	}

	function getFtsDevice(): NetworkDevice | null {
		return (
			networkDevices.find(
				(device) => device.deviceType === 'drone' && device.interfaceRole === 'secondary'
			) || null
		);
	}

	function isLaptopDevice(device: NetworkDevice): boolean {
		return device.deviceType === 'macbook' || device.deviceType === 'linux_computer';
	}

	function isSmartphoneDevice(device: NetworkDevice): boolean {
		return device.deviceType === 'iphone' || device.deviceType === 'android';
	}
	function getStatusColor(status: unknown): string {
		if (!status) return 'gray';
		const statusName = status.name || status.toString();
		switch (statusName.toLowerCase()) {
			case 'ready':
				return 'green';
			case 'unavailable':
				return 'yellow';
			case 'grounded':
				return 'red';
			case 'offline':
				return 'dark';
			case 'maintenance due':
				return 'orange';
			case 'post maintenance checks':
				return 'yellow';
			default:
				return 'gray';
		}
	}

	onMount(loadDroneData);
</script>

<svelte:head>
	<title>Drone {droneId} Dashboard</title>
</svelte:head>
<div class="p-6">
	<!-- Header -->
	<div class="mb-6 flex items-center gap-4">
		<h1 class="text-2xl font-bold text-gray-900">Drone {droneId} Dashboard</h1>
		{#if drone?.gliderStatus}
			<Badge color={getStatusColor(drone.gliderStatus)} class="text-sm">
				{drone.gliderStatus.name || drone.gliderStatus}
			</Badge>
		{/if}
	</div>

	{#if loading}
		<!-- Loading State -->
		<div class="flex items-center justify-center py-12">
			<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-green-600"></div>
			<span class="ml-3 text-gray-600">Loading drone data...</span>
		</div>
	{:else if error}
		<!-- Error State -->
		<div class="rounded-lg border border-red-200 bg-red-50 p-4">
			<div class="flex">
				<div class="text-red-600">
					<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
						<path
							fill-rule="evenodd"
							d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
							clip-rule="evenodd"
						></path>
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error</h3>
					<p class="mt-1 text-sm text-red-700">{error}</p>
				</div>
			</div>
		</div>
	{:else if drone}
		<!-- Dashboard Content -->
		<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
			<!-- Identification Card -->
			<Card class="p-6">
				<div class="mb-4 flex items-center justify-between">
					<h3 class="text-lg font-semibold text-gray-900">Identification</h3>
					<Button
						size="sm"
						color="alternative"
						on:click={openEditModal}
						class="flex items-center gap-2"
					>
						<EditOutline class="h-4 w-4" />
						Edit
					</Button>
				</div>
				<div class="space-y-3">
					<div>
						<span class="text-sm text-gray-500">Drone ID:</span>
						<span class="block font-medium">{drone.id}</span>
					</div>
					<div>
						<span class="text-sm text-gray-500">Drone Name:</span>
						<span class="block font-medium">{drone.name || `${drone.line}-${drone.number}`}</span>
					</div>
					<div>
						<span class="text-sm text-gray-500">Pixhawk:</span>
						<span class="block break-all text-xs font-medium">{drone.pixhawkUuid || 'Unknown'}</span
						>
					</div>
					<div>
						<span class="text-sm text-gray-500">Region:</span>
						<span class="block font-medium">{drone.region?.name || drone.region || 'Unknown'}</span>
					</div>
					<div>
						<span class="text-sm text-gray-500">Manufacturing Date:</span>
						<span class="block font-medium">{formatDate(drone.manufacturingDate)}</span>
					</div>
				</div>
			</Card>

			<!-- Status Card -->
			<Card class="p-6">
				<h3 class="mb-4 text-lg font-semibold text-gray-900">Status</h3>
				<div class="space-y-3">
					<div>
						<span class="text-sm text-gray-500">Status:</span>
						<Badge color={getStatusColor(drone.gliderStatus)} class="ml-2">
							{drone.gliderStatus?.name || drone.gliderStatus || 'Unknown'}
						</Badge>
					</div>
				</div>
			</Card>

			<!-- Flight Summary Card -->
			<Card class="p-6">
				<h3 class="mb-4 text-lg font-semibold text-gray-900">Flight Summary</h3>
				<div class="space-y-3">
					<div>
						<span class="text-sm text-gray-500">Total Flight Hours:</span>
						<span class="block font-medium"
							>{Math.floor(drone.totalFlightTimeInSeconds / 3600)}h {Math.floor(
								(drone.totalFlightTimeInSeconds % 3600) / 60
							)}m {drone.totalFlightTimeInSeconds % 60}s</span
						>
					</div>
					<div>
						<span class="text-sm text-gray-500">Flight Hours Since Last Maintenance:</span>
						<span class="block font-medium"
							>{Math.floor(drone.totalFlightTimeSinceLastMaintenanceInSeconds / 3600)}h {Math.floor(
								(drone.totalFlightTimeSinceLastMaintenanceInSeconds % 3600) / 60
							)}m {drone.totalFlightTimeSinceLastMaintenanceInSeconds % 60}s</span
						>
					</div>
				</div>
			</Card>
		</div>

		<!-- Network Information -->
		<div class="mt-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
			<!-- Network Drone -->
			{#if getDroneDevice()}
				{@const droneDevice = getDroneDevice()}
				{@const droneName = drone?.name || `${drone?.line}-${drone?.number}`}
				<Card class="border-l-4 border-green-500 p-4">
					<h4 class="mb-3 font-semibold text-gray-900">
						Network Drone <span class="font-normal text-blue-600">{droneName}</span>
					</h4>

					<div class="mb-3">
						<div class="mb-1 text-sm text-gray-600">Nebula IP Address:</div>
						<div class="font-medium">{droneDevice.ipAddress}</div>
					</div>

					<div class="mb-3">
						<div class="text-sm text-gray-600">Last online Nebula:</div>
						<div class="text-sm font-medium">{droneDevice.lastOnline}</div>
					</div>
				</Card>
			{:else}
				<Card class="border-l-4 border-gray-500 p-4">
					<h4 class="mb-3 font-semibold text-gray-900">Network Drone</h4>
					<div class="text-sm text-gray-500">No network device found for this drone</div>
				</Card>
			{/if}

			<Card class="border-l-4 border-blue-500 p-4">
				<h4 class="mb-3 font-semibold text-gray-900">Network My Laptop</h4>
				<div class="mb-3">
					<Select bind:value={selectedLaptopUserEmail} class="text-sm" placeholder="Select user...">
						{#each users as user}
							<option value={user.email}>{user.email}</option>
						{/each}
					</Select>
				</div>

				{#if selectedLaptopUserEmail}
					{#if networkDevices.filter((d) => d.email === selectedLaptopUserEmail && isLaptopDevice(d)).length > 0}
						<div class="space-y-1">
							<div class="grid grid-cols-3 gap-2 border-b pb-1 text-xs font-medium text-gray-600">
								<div>IP Address</div>
								<div>Name</div>
								<div>Last Online</div>
							</div>
							{#each networkDevices
								.filter((device) => device.email === selectedLaptopUserEmail && isLaptopDevice(device))
								.slice(0, 3) as device}
								<div class="grid grid-cols-3 gap-2 py-1 text-xs">
									<div class="font-medium">{device.ipAddress}</div>
									<div>{device.name}</div>
									<div>
										{#if device.lastOnline.includes('sec') || device.lastOnline.includes('min')}
											<span class="font-medium text-green-600">ONLINE</span>
										{:else}
											<span>{device.lastOnline}</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<div class="text-sm text-gray-500">No laptop device found for this user.</div>
					{/if}
				{:else}
					<div class="text-sm text-gray-500">Please select a user.</div>
				{/if}
			</Card>

			<!-- Network FTS -->
			{#if getFtsDevice()}
				{@const ftsDevice = getFtsDevice()}
				{@const ftsName = ftsDevice.name.includes('FTS')
					? ftsDevice.name
					: `${drone?.name || `${drone?.line}-${drone?.number}`} FTS`}
				<Card class="border-l-4 border-purple-500 p-4">
					<h4 class="mb-3 font-semibold text-gray-900">
						Network FTS <span class="font-normal text-blue-600">{ftsName}</span>
					</h4>

					<div class="mb-3">
						<div class="mb-1 text-sm text-gray-600">Nebula IP Address:</div>
						<div class="font-medium">{ftsDevice.ipAddress}</div>
					</div>

					<div class="mb-3">
						<div class="text-sm text-gray-600">Last online Nebula:</div>
						<div class="text-sm font-medium">
							{#if ftsDevice.lastOnline.includes('sec') || ftsDevice.lastOnline.includes('min')}
								<span class="font-medium text-green-600">ONLINE</span>
							{:else}
								{ftsDevice.lastOnline}
							{/if}
						</div>
					</div>
				</Card>
			{:else}
				<Card class="border-l-4 border-gray-500 p-4">
					<h4 class="mb-3 font-semibold text-gray-900">Network FTS</h4>
					<div class="space-y-2 text-sm">
						<div class="text-gray-500">No FTS device found for this drone</div>
					</div>
				</Card>
			{/if}

			<!-- Network Smartphone for FTS -->
			<Card class="border-l-4 border-orange-500 p-4">
				<h4 class="mb-3 font-semibold text-gray-900">Network Smartphone for FTS</h4>
				<div class="mb-3">
					<Select
						bind:value={selectedSmartphoneUserEmail}
						class="text-sm"
						placeholder="Select user..."
					>
						{#each users as user}
							<option value={user.email}>{user.email}</option>
						{/each}
					</Select>
				</div>

				{#if selectedSmartphoneUserEmail}
					{#if networkDevices.filter((d) => d.email === selectedSmartphoneUserEmail && isSmartphoneDevice(d)).length > 0}
						<div class="space-y-1">
							<div class="grid grid-cols-3 gap-2 border-b pb-1 text-xs font-medium text-gray-600">
								<div>IP Address</div>
								<div>Name</div>
								<div>Last Online</div>
							</div>
							{#each networkDevices
								.filter((device) => device.email === selectedSmartphoneUserEmail && isSmartphoneDevice(device))
								.slice(0, 3) as device}
								<div class="grid grid-cols-3 gap-2 py-1 text-xs">
									<div class="font-medium">{device.ipAddress}</div>
									<div>{device.name}</div>
									<div>
										{#if device.lastOnline.includes('sec') || device.lastOnline.includes('min')}
											<span class="font-medium text-green-600">ONLINE</span>
										{:else}
											<span>{device.lastOnline}</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<div class="text-sm text-gray-500">No smartphone device found for this user.</div>
					{/if}
				{:else}
					<div class="text-sm text-gray-500">Please select a user.</div>
				{/if}
			</Card>
		</div>

		<!-- Software Versions Section - Full Width -->
		<div class="mt-6 w-full">
			<Card class="w-full p-6" style="width: 100%; max-width: none;">
				<div class="mb-4 flex items-center justify-between">
					<h3 class="text-lg font-semibold text-gray-900">Software Versions</h3>
					<Button size="xs" color="light">✏️</Button>
				</div>
				<div class="w-full overflow-x-auto" style="width: 100%;">
					<Table class="w-full" style="width: 100%; table-layout: auto;">
						<TableHead>
							<TableHeadCell>Component</TableHeadCell>
							<TableHeadCell>Current Version</TableHeadCell>
							<TableHeadCell>Desired Version</TableHeadCell>
							<TableHeadCell>Known Issues of Current Version</TableHeadCell>
							<TableHeadCell></TableHeadCell>
						</TableHead>
						<TableBody>
							<TableBodyRow>
								<TableBodyCell class="font-medium">Jetson SW</TableBodyCell>
								<TableBodyCell>{drone.jetsonSoftwareVersion?.version || 'N/A'}</TableBodyCell>
								<TableBodyCell>{drone.desiredJetsonSoftwareVersion?.version || 'N/A'}</TableBodyCell
								>
								<TableBodyCell>
									<Badge color="gray" class="text-xs">No known issues</Badge>
								</TableBodyCell>
								<TableBodyCell>
									<Button size="xs" color="light">✏️</Button>
								</TableBodyCell>
							</TableBodyRow>
							<TableBodyRow>
								<TableBodyCell class="font-medium">Autopilot SW</TableBodyCell>
								<TableBodyCell>{drone.autopilotSoftwareVersion?.version || 'N/A'}</TableBodyCell>
								<TableBodyCell
									>{drone.desiredAutopilotSoftwareVersion?.version || 'N/A'}</TableBodyCell
								>
								<TableBodyCell>
									<Badge color="gray" class="text-xs">No known issues</Badge>
								</TableBodyCell>
								<TableBodyCell>
									<Button size="xs" color="light">✏️</Button>
								</TableBodyCell>
							</TableBodyRow>
							<TableBodyRow>
								<TableBodyCell class="font-medium">FTS Pixhawk SW</TableBodyCell>
								<TableBodyCell>{drone.ftsPixhawkSoftwareVersion?.version || 'N/A'}</TableBodyCell>
								<TableBodyCell
									>{drone.desiredFtsPixhawkSoftwareVersion?.version || 'N/A'}</TableBodyCell
								>
								<TableBodyCell>
									<Badge color="gray" class="text-xs">No known issues</Badge>
								</TableBodyCell>
								<TableBodyCell>
									<Button size="xs" color="light">✏️</Button>
								</TableBodyCell>
							</TableBodyRow>
							<TableBodyRow>
								<TableBodyCell class="font-medium">FTS Raspi SW</TableBodyCell>
								<TableBodyCell>{drone.ftsRaspiSoftwareVersion?.version || 'N/A'}</TableBodyCell>
								<TableBodyCell
									>{drone.desiredFtsRaspiSoftwareVersion?.version || 'N/A'}</TableBodyCell
								>
								<TableBodyCell>
									<Badge color="gray" class="text-xs">No known issues</Badge>
								</TableBodyCell>
								<TableBodyCell>
									<Button size="xs" color="light">✏️</Button>
								</TableBodyCell>
							</TableBodyRow>
							<TableBodyRow>
								<TableBodyCell class="font-medium">Force Sensor</TableBodyCell>
								<TableBodyCell>
									<select class="rounded border px-2 py-1 text-sm">
										<option>Select version...</option>
									</select>
								</TableBodyCell>
								<TableBodyCell>—</TableBodyCell>
								<TableBodyCell>
									<Badge color="gray" class="text-xs">No known issues</Badge>
								</TableBodyCell>
								<TableBodyCell>
									<Button size="xs" color="light">✏️</Button>
								</TableBodyCell>
							</TableBodyRow>
						</TableBody>
					</Table>
				</div>
			</Card>
		</div>
	{/if}
</div>

<!-- Edit Drone Modal -->
<EditDroneModal
	bind:showModal={showEditModal}
	{drone}
	onClose={closeEditModal}
	onSuccess={handleEditSuccess}
/>

<!-- Success Toast -->
{#if showSuccessToast}
	<Toast color="green" position="top-right" class="mb-4">
		<svelte:fragment slot="icon">
			<CheckCircleOutline class="h-5 w-5" />
		</svelte:fragment>
		Drone information updated successfully!
	</Toast>
{/if}
