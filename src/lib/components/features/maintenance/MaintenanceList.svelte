<script lang="ts">
	import { onMount } from 'svelte';
	import { <PERSON><PERSON>, Spinner, Label, Select, ButtonGroup } from 'flowbite-svelte';
	import { PlusOutline, ChevronLeftOutline, ChevronRightOutline } from 'flowbite-svelte-icons';
	import MaintenanceTable from './MaintenanceTable.svelte';
	import MaintenanceFilters from './MaintenanceFilters.svelte';
	import AddEditMaintenanceModal from '$lib/components/common/modals/AddEditMaintenanceModal.svelte';
	import {
		maintenanceStore,
		setMaintenances,
		setLoading,
		setError,
		setPagination,
		setSortState,
		type MaintenanceState
	} from '$lib/stores/maintenance.store';
	import { fetchMaintenances } from '$lib/services/maintenance.service';
	import type { Maintenance } from '$lib/types/maintenance.types';

	let showAddModal = false;
	let selectedMaintenance: Maintenance | null = null;

	$: state = $maintenanceStore as MaintenanceState;

	let currentPage = 1;
	let pageSize = 10; // Default to 10 rows per page
	let totalMaintenances = 0;
	let totalPages = 0;
	let previousPageSize = pageSize;

	const pageSizeOptions = [10, 20];

	$: if (pageSize !== previousPageSize) {
		console.log('Page size changed, calling loadMaintenances');
		previousPageSize = pageSize;
		currentPage = 1; // Reset to first page when changing page size
		setPagination({ page: currentPage, limit: pageSize, total: totalMaintenances });
		loadMaintenances();
	}

	onMount(async () => {
		await loadMaintenances();
	});

	async function loadMaintenances() {
		console.log('loadMaintenances() called');
		setLoading(true);
		try {
			setPagination({ page: currentPage, limit: pageSize, total: totalMaintenances });

			const maintenances = await fetchMaintenances(
				state.filters,
				{
					page: currentPage,
					limit: pageSize,
					total: totalMaintenances
				},
				state.sort
			);
			setMaintenances(maintenances);

			const skip = (currentPage - 1) * pageSize;
			if (maintenances.length < pageSize) {
				totalMaintenances = skip + maintenances.length;
			} else {
				totalMaintenances = Math.max(totalMaintenances, skip + maintenances.length + 1);
			}

			totalPages = Math.ceil(totalMaintenances / pageSize);
		} catch (error) {
			console.error('Error loading maintenance records:', error);
			setError('Failed to load maintenance records');
		}
	}

	function handleAddMaintenance() {
		selectedMaintenance = null;
		showAddModal = true;
	}

	function handleEditMaintenance(maintenance: Maintenance) {
		selectedMaintenance = maintenance;
		showAddModal = true;
	}

	function handleModalClose() {
		showAddModal = false;
		selectedMaintenance = null;
	}

	function handleMaintenanceCreated() {
		showAddModal = false;
		selectedMaintenance = null;
		loadMaintenances();
	}

	function handleFiltersChanged() {
		currentPage = 1; // Reset to first page when filters change
		loadMaintenances();
	}

	async function goToPage(page: number) {
		if (page < 1 || page > totalPages || page === currentPage) return;
		currentPage = page;
		await loadMaintenances();
	}

	function getPageNumbers(): number[] {
		const pages: number[] = [];
		const maxVisiblePages = 5;

		if (totalPages <= maxVisiblePages) {
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			const start = Math.max(1, currentPage - 2);
			const end = Math.min(totalPages, start + maxVisiblePages - 1);

			for (let i = start; i <= end; i++) {
				pages.push(i);
			}
		}

		return pages;
	}
</script>

<div class="flex min-h-screen flex-col py-8">
	<div class="w-full flex-1 px-6">
		<div class="mb-6 flex items-center justify-between">
			<div>
				<h2 class="text-2xl font-bold text-gray-900">Equipment Maintenance</h2>
				<p class="text-gray-600">Manage maintenance records for gliders and equipment</p>
			</div>
			<Button
				size="sm"
				class="flex items-center gap-2 bg-primary-500 hover:bg-primary-600"
				on:click={handleAddMaintenance}
			>
				<PlusOutline class="h-4 w-4" />
				Add Maintenance
			</Button>
		</div>

		<div class="mb-6">
			<MaintenanceFilters on:filtersChanged={handleFiltersChanged} />
		</div>

		{#if state.loading}
			<div class="flex items-center justify-center py-12">
				<Spinner size="8" />
			</div>
		{:else if state.error}
			<div
				class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
				role="alert"
			>
				<p>{state.error}</p>
			</div>
		{:else if state.maintenances.length === 0}
			<div
				class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700"
				role="alert"
			>
				<p>No maintenance records found. Click "Add Maintenance" to create your first record.</p>
			</div>
		{:else}
			<MaintenanceTable
				maintenances={state.maintenances}
				on:editMaintenance={(event) => handleEditMaintenance(event.detail)}
			/>

			<!-- Pagination Controls -->
			{#if !state.loading && state.maintenances.length > 0}
				<div class="mt-6 flex flex-col items-center justify-between gap-4 sm:flex-row">
					<!-- Page Size Selector -->
					<div class="flex items-center gap-2">
						<Label for="page-size" class="text-sm font-medium text-gray-700">Rows per page:</Label>
						<Select id="page-size" bind:value={pageSize} class="w-20">
							{#each pageSizeOptions as size}
								<option value={size}>{size}</option>
							{/each}
						</Select>
					</div>

					<!-- Pagination Info -->
					<div class="text-sm text-gray-700">
						Showing {(currentPage - 1) * pageSize + 1} to {Math.min(
							currentPage * pageSize,
							totalMaintenances
						)} of {totalMaintenances} maintenance records
					</div>

					<!-- Page Navigation -->
					<div class="flex items-center gap-2">
						<!-- Previous Button -->
						<Button
							size="sm"
							color="alternative"
							disabled={currentPage === 1 || state.loading}
							on:click={() => goToPage(currentPage - 1)}
						>
							<ChevronLeftOutline class="h-4 w-4" />
							Previous
						</Button>

						<!-- Page Numbers -->
						<ButtonGroup>
							{#each getPageNumbers() as pageNum}
								<Button
									size="sm"
									color={pageNum === currentPage ? 'primary' : 'alternative'}
									disabled={state.loading}
									on:click={() => goToPage(pageNum)}
								>
									{pageNum}
								</Button>
							{/each}
						</ButtonGroup>

						<!-- Next Button -->
						<Button
							size="sm"
							color="alternative"
							disabled={currentPage === totalPages || state.loading}
							on:click={() => goToPage(currentPage + 1)}
						>
							Next
							<ChevronRightOutline class="h-4 w-4" />
						</Button>
					</div>
				</div>
			{/if}
		{/if}
	</div>
</div>

<AddEditMaintenanceModal
	bind:showModal={showAddModal}
	maintenance={selectedMaintenance}
	on:close={handleModalClose}
	on:maintenanceCreated={handleMaintenanceCreated}
/>
