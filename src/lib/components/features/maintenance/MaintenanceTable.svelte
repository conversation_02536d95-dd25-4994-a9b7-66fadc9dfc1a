<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Button,
		Badge,
		Tooltip
	} from 'flowbite-svelte';
	import { PenOutline, CheckCircleOutline, EditOutline } from 'flowbite-svelte-icons';
	import { compareEquipmentNames } from '$lib/services/maintenance.service';
	import { maintenanceStore, setSort } from '$lib/stores/maintenance.store';
	import type { Maintenance, MaintenanceSortConfig } from '$lib/types/maintenance.types';

	export let maintenances: Maintenance[] = [];

	const dispatch = createEventDispatcher();

	$: state = $maintenanceStore;
	$: sortedMaintenances = sortMaintenances(maintenances, state.sort);

	function sortMaintenances(data: Maintenance[], sort: MaintenanceSortConfig): Maintenance[] {
		if (!data || data.length === 0) return [];

		return [...data].sort((a, b) => {
			let aValue: any;
			let bValue: any;

			switch (sort.field) {
				case 'id':
					aValue = a.id;
					bValue = b.id;
					break;
				case 'completedDateTime':
					aValue = a.completedDateTime || a.createdAt;
					bValue = b.completedDateTime || b.createdAt;
					break;
				case 'equipment':
					return sort.direction === 'asc'
						? compareEquipmentNames(
								a.glider?.name || a.equipment?.name || '',
								b.glider?.name || b.equipment?.name || ''
							)
						: compareEquipmentNames(
								b.glider?.name || b.equipment?.name || '',
								a.glider?.name || a.equipment?.name || ''
							);
				case 'dueDateTime':
					aValue = a.dueDateTime;
					bValue = b.dueDateTime;
					break;
				default:
					aValue = a[sort.field];
					bValue = b[sort.field];
			}

			if (aValue === bValue) return 0;

			const modifier = sort.direction === 'asc' ? 1 : -1;

			if (aValue === null || aValue === undefined) return 1 * modifier;
			if (bValue === null || bValue === undefined) return -1 * modifier;

			if (typeof aValue === 'string' && typeof bValue === 'string') {
				return aValue.localeCompare(bValue) * modifier;
			}

			return (aValue < bValue ? -1 : 1) * modifier;
		});
	}

	function handleSort(field: keyof Maintenance) {
		const currentSort = state.sort;
		const newDirection =
			currentSort.field === field && currentSort.direction === 'asc' ? 'desc' : 'asc';
		setSort({ field, direction: newDirection });
	}

	function handleEdit(maintenance: Maintenance) {
		dispatch('editMaintenance', maintenance);
	}

	function formatDate(dateString: string | undefined): string {
		if (!dateString) return '-';
		return new Date(dateString).toLocaleString('en-GB', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getMaintenanceTypeColor(type: string): string {
		switch (type.toLowerCase()) {
			case 'scheduled maintenance':
				return 'blue';
			case 'software update':
				return 'purple';
			case 'emergency repair':
				return 'red';
			case 'unscheduled maintenance':
				return 'yellow';
			default:
				return 'gray';
		}
	}

	function getSortIcon(field: keyof Maintenance): string {
		if (state.sort.field !== field) return '';
		return state.sort.direction === 'asc' ? '↑' : '↓';
	}
</script>

<div class="overflow-x-auto">
	<Table striped={true} hoverable={true} class="rounded-lg bg-white shadow">
		<TableHead class="bg-gray-100">
			<TableHeadCell
				class="w-20 cursor-pointer font-medium text-gray-700 hover:bg-gray-200"
				on:click={() => handleSort('id')}
			>
				<div class="flex items-center">
					<span>ID</span>
					<span class="ml-1 text-primary-500">{getSortIcon('id')}</span>
				</div>
			</TableHeadCell>

			<TableHeadCell
				class="cursor-pointer font-medium text-gray-700 hover:bg-gray-200"
				on:click={() => handleSort('completedDateTime')}
			>
				<div class="flex items-center">
					<span>Completed Date</span>
					<span class="ml-1 text-primary-500">{getSortIcon('completedDateTime')}</span>
				</div>
			</TableHeadCell>

			<TableHeadCell
				class="cursor-pointer font-medium text-gray-700 hover:bg-gray-200"
				on:click={() => handleSort('equipment')}
			>
				<div class="flex items-center">
					<span>Equipment</span>
					<span class="ml-1 text-primary-500">{getSortIcon('equipment')}</span>
				</div>
			</TableHeadCell>

			<TableHeadCell
				class="cursor-pointer font-medium text-gray-700 hover:bg-gray-200"
				on:click={() => handleSort('dueDateTime')}
			>
				<div class="flex items-center">
					<span>Due Date</span>
					<span class="ml-1 text-primary-500">{getSortIcon('dueDateTime')}</span>
				</div>
			</TableHeadCell>

			<TableHeadCell class="w-24 font-medium text-gray-700">Type</TableHeadCell>

			<TableHeadCell class="font-medium text-gray-700">
				<div class="flex items-center">
					<span>Notes</span>
					<Tooltip>Maintenance notes and descriptions</Tooltip>
				</div>
			</TableHeadCell>

			<TableHeadCell class="w-24 text-center font-medium text-gray-700">
				<span>Post Check</span>
			</TableHeadCell>

			<TableHeadCell class="w-20 text-center font-medium text-gray-700">Actions</TableHeadCell>
		</TableHead>

		<TableBody class="divide-y">
			{#each sortedMaintenances as maintenance (maintenance.id)}
				<TableBodyRow class="hover:bg-gray-50">
					<TableBodyCell class="text-center font-medium text-gray-900">
						{maintenance.id}
					</TableBodyCell>

					<TableBodyCell class="font-medium text-gray-900">
						{formatDate(maintenance.completedDateTime || maintenance.createdAt)}
					</TableBodyCell>

					<TableBodyCell>
						<span class="font-medium text-primary-600">
							{maintenance.glider?.name || maintenance.equipment?.name || 'Unknown Equipment'}
						</span>
					</TableBodyCell>

					<TableBodyCell class="text-gray-900">
						{formatDate(maintenance.dueDateTime)}
					</TableBodyCell>

					<TableBodyCell>
						<Badge color={getMaintenanceTypeColor(maintenance.maintenanceType?.name || '')}>
							{maintenance.maintenanceType?.name || 'Unknown Type'}
						</Badge>
					</TableBodyCell>

					<TableBodyCell class="text-left">
						<div class="group relative">
							<div class="max-w-xs cursor-help truncate text-sm text-gray-700">
								{maintenance.notes || 'No notes'}
							</div>
							{#if maintenance.notes && maintenance.notes.length > 50}
								<div
									class="invisible absolute bottom-full left-0 z-50 mb-2 w-80 translate-y-1 transform rounded-lg bg-gray-900 px-3 py-2 text-xs text-white opacity-0 shadow-lg transition-all duration-300 group-hover:visible group-hover:translate-y-0 group-hover:opacity-100"
								>
									<div class="max-h-40 overflow-y-auto whitespace-pre-wrap break-words">
										{maintenance.notes}
									</div>
									<div
										class="absolute left-4 top-full h-0 w-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"
									></div>
								</div>
							{/if}
						</div>
					</TableBodyCell>

					<TableBodyCell class="text-center">
						{#if maintenance.postMaintenanceChecklistDone === true}
							<CheckCircleOutline class="mx-auto h-4 w-4 text-green-500" />
						{:else}
							<div class="mx-auto h-4 w-4 rounded border border-gray-300"></div>
						{/if}
					</TableBodyCell>

					<TableBodyCell class="text-center">
						<Button size="xs" color="primary" class="p-1" on:click={() => handleEdit(maintenance)}>
							<EditOutline class="h-3 w-3" />
						</Button>
					</TableBodyCell>
				</TableBodyRow>
			{/each}
		</TableBody>
	</Table>
</div>
