<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Input, Select, Button, Label } from 'flowbite-svelte';
	import { FilterSolid, TrashBinSolid } from 'flowbite-svelte-icons';
	import { maintenanceStore, setFilters, clearFilters } from '$lib/stores/maintenance.store';
	import { fetchGliders } from '$lib/services/maintenance.service';
	import { fetchMaintenanceTypes } from '$lib/services/maintenance-types.service';
	import type { Equipment, MaintenanceType } from '$lib/types/maintenance.types';

	const dispatch = createEventDispatcher();

	let gliders: Equipment[] = [];
	let maintenanceTypes: MaintenanceType[] = [];
	let loading = false;

	$: state = $maintenanceStore;
	$: filters = state.filters;

	onMount(async () => {
		await loadFilterData();
	});

	async function loadFilterData() {
		loading = true;
		try {
			const [glidersData, typesData] = await Promise.all([fetchGliders(), fetchMaintenanceTypes()]);
			gliders = glidersData;
			maintenanceTypes = typesData;
		} catch (error) {
		} finally {
			loading = false;
		}
	}

	function handleFilterChange() {
		setFilters(filters);
		dispatch('filtersChanged');
	}

	function handleClearFilters() {
		clearFilters();
		dispatch('filtersChanged');
	}

	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;
		filters.searchTerm = target.value;
		handleFilterChange();
	}

	function handleEquipmentChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		filters.equipmentId = target.value ? Number(target.value) : undefined;
		handleFilterChange();
	}

	function handleTypeChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		filters.maintenanceTypeId = target.value ? Number(target.value) : undefined;
		handleFilterChange();
	}

	function handleDateFromChange(event: Event) {
		const target = event.target as HTMLInputElement;
		filters.dateFrom = target.value || undefined;
		handleFilterChange();
	}

	function handleDateToChange(event: Event) {
		const target = event.target as HTMLInputElement;
		filters.dateTo = target.value || undefined;
		handleFilterChange();
	}
</script>

<div class="rounded border bg-white p-3">
	<div class="grid grid-cols-2 gap-3 md:grid-cols-6">
		<div>
			<Label for="search" class="mb-1 text-sm">Search</Label>
			<Input
				id="search"
				type="text"
				placeholder="Search notes..."
				size="sm"
				value={filters.searchTerm || ''}
				on:input={handleSearchInput}
			/>
		</div>

		<div>
			<Label for="equipment" class="mb-1 text-sm">Equipment</Label>
			<Select
				id="equipment"
				size="sm"
				disabled={loading}
				value={filters.equipmentId || ''}
				on:change={handleEquipmentChange}
			>
				<option value="">All</option>
				{#each gliders as glider}
					<option value={glider.id}>{glider.name}</option>
				{/each}
			</Select>
		</div>

		<div>
			<Label for="type" class="mb-1 text-sm">Type</Label>
			<Select
				id="type"
				size="sm"
				disabled={loading}
				value={filters.maintenanceTypeId || ''}
				on:change={handleTypeChange}
			>
				<option value="">All</option>
				{#each maintenanceTypes as type}
					<option value={type.id}>{type.name}</option>
				{/each}
			</Select>
		</div>

		<div>
			<Label for="dateFrom" class="mb-1 text-sm">From</Label>
			<Input
				id="dateFrom"
				type="date"
				size="sm"
				value={filters.dateFrom || ''}
				on:input={handleDateFromChange}
			/>
		</div>

		<div>
			<Label for="dateTo" class="mb-1 text-sm">To</Label>
			<Input
				id="dateTo"
				type="date"
				size="sm"
				value={filters.dateTo || ''}
				on:input={handleDateToChange}
			/>
		</div>
		<div class="flex items-end">
			<Button color="light" size="sm" class="w-full" on:click={handleClearFilters}>Clear</Button>
		</div>
	</div>
</div>
