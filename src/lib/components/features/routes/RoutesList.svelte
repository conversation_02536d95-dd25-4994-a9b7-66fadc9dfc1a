<script lang="ts">
	import { onMount } from 'svelte';
	import { fetchRoutes, type Route } from '$lib/services/routes.service';
	import TimeTrackingButton from './TimeTrackingButton.svelte';
	import SimpleTimeTrackingButton from './SimpleTimeTrackingButton.svelte';
	import {
		Button,
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Spinner,
		Select,
		Label,
		ButtonGroup
	} from 'flowbite-svelte';
	import { DownloadSolid, ChevronLeftOutline, ChevronRightOutline } from 'flowbite-svelte-icons';
	import AddEditRouteModal from '$lib/components/common/modals/AddEditRouteModal.svelte';
	import StartTrackingModal from './StartTrackingModal.svelte';
	import { goto } from '$app/navigation';
	import { get } from 'svelte/store';
	import { keycloakClient } from '$lib/stores';

	const useSimpleButton = true;

	let routes: Route[] = [];
	let loading = true;
	let error: string | null = null;
	let showModal = false;
	let showStartTrackingModal = false;
	let selectedRouteId: number | null = null;

	let currentPage = 1;
	let pageSize = 10;
	let totalRoutes = 0;
	let totalPages = 0;
	let previousPageSize = pageSize;

	const pageSizeOptions = [10, 20];

	$: if (pageSize !== previousPageSize) {
		previousPageSize = pageSize;
		currentPage = 1;
		loadRoutes();
	}

	async function loadRoutes() {
		try {
			loading = true;
			error = null;
			const skip = (currentPage - 1) * pageSize;

			const routesData = await fetchRoutes(skip, pageSize);

			routes = routesData;

			if (routesData.length < pageSize) {
				totalRoutes = skip + routesData.length;
			} else {
				totalRoutes = Math.max(totalRoutes, skip + routesData.length + 1);
			}

			totalPages = Math.ceil(totalRoutes / pageSize);
		} catch (err) {
			console.error('Error loading routes:', err);
			error = err instanceof Error ? err.message : 'Unknown error loading routes';
		} finally {
			loading = false;
		}
	}

	onMount(async () => {
		const keycloak = get(keycloakClient);
		if (!keycloak?.token) {
			error = 'Authentication token not found';
			loading = false;
			return;
		}

		await loadRoutes();
	});

	const openModal = () => {
		showModal = true;
	};

	const closeModal = async () => {
		showModal = false;
		await loadRoutes();
	};

	const navigateToEditPage = (routeId) => {
		goto(`/routes/edit?id=${routeId}`);
	};

	const openStartTrackingModal = (routeId: number) => {
		selectedRouteId = routeId;
		showStartTrackingModal = true;
	};

	const closeStartTrackingModal = () => {
		showStartTrackingModal = false;
		selectedRouteId = null;
	};

	const handleTrackingStarted = async () => {
		showStartTrackingModal = false;
		selectedRouteId = null;
	};

	async function goToPage(page: number) {
		if (page < 1 || page > totalPages || page === currentPage) return;
		currentPage = page;
		await loadRoutes();
	}

	function getPageNumbers(): number[] {
		const pages: number[] = [];
		const maxVisiblePages = 5;

		if (totalPages <= maxVisiblePages) {
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			const start = Math.max(1, currentPage - 2);
			const end = Math.min(totalPages, start + maxVisiblePages - 1);

			for (let i = start; i <= end; i++) {
				pages.push(i);
			}
		}

		return pages;
	}
</script>

<div class="flex min-h-screen flex-col py-8">
	<div class="w-full flex-1 px-6">
		<div class="mb-6 flex items-center justify-between">
			<div>
				<h2 class="text-2xl font-bold">Routes & Time Tracking</h2>
				<p class="text-gray-600">Manage routes and track time spent on each route</p>
			</div>
			<Button size="xs" class="flex items-center gap-2 bg-primary-500" on:click={openModal}>
				<DownloadSolid class="h-4 w-4" />
				Add New Route
			</Button>
		</div>
		<div class="rounded-lg bg-white p-4 shadow">
			{#if loading}
				<div class="flex flex-col items-center justify-center py-12">
					<Spinner size="8" class="common-spinner mb-4" />
					<p class="text-gray-500">Loading routes data...</p>
				</div>
			{:else if error}
				<div
					class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
					role="alert"
				>
					<p>{error}</p>
				</div>
			{:else if routes.length === 0}
				<div
					class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700"
					role="alert"
				>
					<p>No routes available</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<Table
						striped={true}
						hoverable={true}
						class="rounded-lg bg-white shadow"
						data-testid="routes-table"
					>
						<TableHead>
							<TableHeadCell class="w-16">ID</TableHeadCell>
							<TableHeadCell class="w-1/6">External ID</TableHeadCell>
							<TableHeadCell class="w-1/4">Route</TableHeadCell>
							<TableHeadCell class="w-1/6">Customer</TableHeadCell>
							<TableHeadCell class="w-1/4">
								<div class="flex items-center justify-end">
									Actions & Time Tracking
									<span class="ml-1 text-xs font-normal text-gray-400"
										>(track time spent on routes)</span
									>
								</div>
							</TableHeadCell>
						</TableHead>
						<TableBody class="divide-y">
							{#each routes as route (route.id)}
								<TableBodyRow>
									<TableBodyCell>{route.id}</TableBodyCell>
									<TableBodyCell class="font-medium text-gray-700"
										>{route.external_route_id || '-'}</TableBodyCell
									>
									<TableBodyCell>
										<div class="flex flex-wrap items-center py-1">
											<span class="break-words font-medium text-gray-800"
												>{route.start_location_name}</span
											>
											<span
												class="mx-2 text-lg font-bold text-primary-500"
												title="Bidirectional route">⬌</span
											>
											<span class="break-words font-medium text-gray-800"
												>{route.end_location_name}</span
											>
										</div>
									</TableBodyCell>
									<TableBodyCell class="font-medium text-gray-700"
										>{route.customer ? route.customer.name : 'Unknown'}</TableBodyCell
									>
									<TableBodyCell>
										<div class="flex w-full flex-row items-center justify-end gap-3">
											<button
												class="flex items-center gap-1 rounded px-2 py-1 text-blue-500 transition-colors hover:bg-gray-100 hover:text-blue-700"
												on:click={() => navigateToEditPage(route.id)}
												style="min-width: 70px; transition: all 0.15s ease; box-shadow: 0 1px 2px rgba(0,0,0,0.05);"
											>
												<span class="text-blue-500">✎</span>
												<span>Edit</span>
											</button>
											<div class="time-tracking-wrapper">
												{#if useSimpleButton}
													<SimpleTimeTrackingButton
														routeId={route.id}
														routeInfo={{
															start_location_name: route.start_location_name,
															end_location_name: route.end_location_name
														}}
														onStartTrackingClick={openStartTrackingModal}
													/>
												{:else}
													<TimeTrackingButton
														routeId={route.id}
														routeInfo={{
															start_location_name: route.start_location_name,
															end_location_name: route.end_location_name
														}}
														onStartTrackingClick={openStartTrackingModal}
													/>
												{/if}
											</div>
										</div>
									</TableBodyCell>
								</TableBodyRow>
							{/each}
						</TableBody>
					</Table>
				</div>

				{#if !loading && routes.length > 0}
					<div class="mt-6 flex flex-col items-center justify-between gap-4 sm:flex-row">
						<div class="flex items-center gap-2">
							<Label for="page-size" class="text-sm font-medium text-gray-700">Rows per page:</Label
							>
							<Select id="page-size" bind:value={pageSize} class="w-20">
								{#each pageSizeOptions as size}
									<option value={size}>{size}</option>
								{/each}
							</Select>
						</div>

						<div class="text-sm text-gray-700">
							Showing {(currentPage - 1) * pageSize + 1} to {Math.min(
								currentPage * pageSize,
								totalRoutes
							)} of {totalRoutes} routes
						</div>

						<div class="flex items-center gap-2">
							<Button
								size="sm"
								color="alternative"
								disabled={currentPage === 1 || loading}
								on:click={() => goToPage(currentPage - 1)}
							>
								<ChevronLeftOutline class="h-4 w-4" />
								Previous
							</Button>
							<ButtonGroup>
								{#each getPageNumbers() as pageNum}
									<Button
										size="sm"
										color={pageNum === currentPage ? 'primary' : 'alternative'}
										disabled={loading}
										on:click={() => goToPage(pageNum)}
									>
										{pageNum}
									</Button>
								{/each}
							</ButtonGroup>
							<Button
								size="sm"
								color="alternative"
								disabled={currentPage === totalPages || loading}
								on:click={() => goToPage(currentPage + 1)}
							>
								Next
								<ChevronRightOutline class="h-4 w-4" />
							</Button>
						</div>
					</div>
				{/if}
			{/if}

			<AddEditRouteModal bind:showModal routeData={null} onClose={closeModal} />
			<StartTrackingModal
				bind:showModal={showStartTrackingModal}
				{routes}
				{selectedRouteId}
				onClose={closeStartTrackingModal}
				onTrackingStarted={handleTrackingStarted}
			/>
		</div>
	</div>
</div>

<style>
	:global(.routes-table) {
		@apply rounded-lg bg-white p-4 shadow;
	}

	:global(.time-tracking-wrapper) {
		transition: all 0.2s ease-in-out;
	}

	:global(.time-tracking-wrapper:hover) {
		transform: translateY(-1px);
	}
</style>
