<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		startTimeTracking,
		stopTimeTracking,
		getLastShift
	} from '$lib/services/time-tracking.service';
	import { userProfile, keycloakClient } from '$lib/stores';
	import { setActiveRoute } from '$lib/stores/time-tracking.store';
	import { get } from 'svelte/store';

	export let routeId: number;
	export let routeInfo: { start_location_name?: string; end_location_name?: string } = {};
	export let onStartTrackingClick: ((routeId: number) => void) | null = null;

	let pilotEmail = '<EMAIL>';
	let errorMessage = '';
	let showAlert = false;
	let currentShiftId: number | null = null;

	function getActualEmail() {
		if ($userProfile && $userProfile.email) {
			return $userProfile.email;
		}

		const keycloak = get(keycloakClient);
		if (keycloak && keycloak.tokenParsed) {
			const email = keycloak.tokenParsed.email;
			return email || pilotEmail;
		}

		return pilotEmail;
	}

	$: {
		if ($userProfile && $userProfile.email) {
			pilotEmail = $userProfile.email;
		}
	}

	$: {
		if ($keycloakClient && $keycloakClient.tokenParsed && $keycloakClient.tokenParsed.email) {
			pilotEmail = $keycloakClient.tokenParsed.email;
		}
	}

	let isTracking = false;
	let trackingStartTime: Date | null = null;
	let elapsedTime = '00:00:00';
	let isLoading = false;
	let timer: number;
	let hasActiveShift = false;
	let activeShiftRouteId: number | null = null;

	onMount(async () => {
		pilotEmail = getActualEmail();

		try {
			await checkLastShift();
		} catch (error) {}
	});

	onDestroy(() => {
		if (timer) {
			clearInterval(timer);
		}
	});

	async function checkLastShift() {
		try {
			console.log('Checking last shift for email:', pilotEmail);
			const lastShift = await getLastShift(pilotEmail);
			console.log('Last shift result:', lastShift);

			hasActiveShift = false;
			activeShiftRouteId = null;

			if (lastShift && !lastShift.stop_time) {
				hasActiveShift = true;
				activeShiftRouteId = lastShift.route_id;

				if (lastShift.route_id === routeId) {
					isTracking = true;
					currentShiftId = lastShift.shift_id || null;
					const startTimeStr = lastShift.start_time;
					trackingStartTime = new Date(startTimeStr + 'Z');
					updateElapsedTime();
					startTimer();
				}
			}
		} catch (error) {
			// Error checking tracking status
		}
	}

	function startTimer() {
		timer = setInterval(updateElapsedTime, 1000);
	}

	function updateElapsedTime() {
		if (!trackingStartTime) return;

		const now = new Date();
		const diff = now.getTime() - trackingStartTime.getTime();

		const totalSeconds = Math.floor(diff / 1000);
		const hours = Math.floor(totalSeconds / 3600)
			.toString()
			.padStart(2, '0');
		const minutes = Math.floor((totalSeconds % 3600) / 60)
			.toString()
			.padStart(2, '0');
		const seconds = Math.floor(totalSeconds % 60)
			.toString()
			.padStart(2, '0');

		elapsedTime = `${hours}:${minutes}:${seconds}`;
	}

	async function toggleTracking() {
		if (isLoading) return;

		isLoading = true;
		errorMessage = '';
		showAlert = false;

		try {
			pilotEmail = getActualEmail();

			const keycloak = get(keycloakClient);
			if (!keycloak?.token) {
				errorMessage = 'Authorization required. Please login again.';
				showAlert = true;
				return;
			}

			if (isTracking) {
				if (currentShiftId) {
					const result = await stopTimeTracking(currentShiftId);

					if (result) {
						isTracking = false;
						currentShiftId = null;
						clearInterval(timer);

						setActiveRoute(null);

						elapsedTime = '00:00:00';
						trackingStartTime = null;

						await checkLastShift();
					} else {
						errorMessage = 'Failed to stop tracking. Please try again.';
						showAlert = true;
					}
				} else {
					errorMessage = 'No active shift found';
					showAlert = true;
				}
			} else {
				await checkLastShift();

				if (hasActiveShift && activeShiftRouteId !== routeId) {
					errorMessage = `You already have an active shift on route #${activeShiftRouteId}. Complete it before starting a new one.`;
					showAlert = true;
				} else {
					if (onStartTrackingClick) {
						onStartTrackingClick(routeId);
						return;
					}

					const defaultDescription =
						routeInfo.start_location_name && routeInfo.end_location_name
							? `Work on route: ${routeInfo.start_location_name} ⬌ ${routeInfo.end_location_name}`
							: `Work on route #${routeId}`;

					const trackingEvent = await startTimeTracking(routeId, pilotEmail, defaultDescription);
					if (trackingEvent && trackingEvent.shift_id) {
						currentShiftId = trackingEvent.shift_id;
						isTracking = true;
						trackingStartTime = new Date();
						updateElapsedTime();
						startTimer();
					} else {
						errorMessage = 'Failed to start tracking';
						showAlert = true;
					}
				}
			}
		} catch (error) {
			errorMessage =
				error instanceof Error ? error.message : 'An error occurred while managing time tracking';
			showAlert = true;
		} finally {
			isLoading = false;
		}
	}
</script>

<div
	class="custom-time-tracking-wrapper"
	style="display: block; border: 1px solid #ddd; padding: 8px; border-radius: 6px; background-color: #f9f9f9; min-width: 150px;"
>
	{#if showAlert}
		<div
			class="custom-alert"
			style="background-color: #fee2e2; color: #b91c1c; padding: 6px; border-radius: 4px; margin-bottom: 8px; font-size: 0.75rem;"
		>
			{errorMessage}
		</div>
	{/if}

	<div class="custom-button-container" style="display: flex; align-items: center; gap: 8px;">
		{#if isTracking}
			<div style="display: flex; align-items: center;">
				<div
					class="custom-tracking-time"
					style="display: flex; align-items: center; margin-right: 8px;"
				>
					<span style="font-size: 0.875rem; font-weight: 500;">⏱️ {elapsedTime}</span>
				</div>
				<button
					on:click={(e) => {
						e.preventDefault();
						e.stopPropagation();
						toggleTracking();
					}}
					disabled={isLoading}
					style="background-color: #ef4444; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 0.75rem; cursor: pointer; display: flex; align-items: center; gap: 4px;"
				>
					{#if isLoading}
						<span
							style="display: inline-block; width: 12px; height: 12px; border: 2px solid currentColor; border-radius: 50%; border-right-color: transparent; animation: spin 1s linear infinite;"
						></span>
					{:else}
						<span>■</span>
					{/if}
					Stop Tracking
				</button>
			</div>
		{:else if hasActiveShift && activeShiftRouteId !== routeId}
			<span style="font-size: 0.75rem; color: #b45309;"
				>Active tracking on route #{activeShiftRouteId}</span
			>
		{:else}
			<button
				on:click={(e) => {
					e.preventDefault();
					e.stopPropagation();
					toggleTracking();
				}}
				disabled={isLoading}
				style="background-color: #48a851; color: white; border: none; border-radius: 4px; padding: 4px 8px; font-size: 0.75rem; cursor: pointer; display: flex; align-items: center; gap: 4px;"
			>
				{#if isLoading}
					<span
						style="display: inline-block; width: 12px; height: 12px; border: 2px solid currentColor; border-radius: 50%; border-right-color: transparent; animation: spin 1s linear infinite;"
					></span>
				{:else}
					<span>▶</span>
				{/if}
				Start Tracking
			</button>
		{/if}
	</div>

	<div style="margin-top: 4px; font-size: 0.75rem; color: #6b7280;">
		Route ID: {routeId} | Email: {pilotEmail ? pilotEmail.substring(0, 5) + '...' : 'Not set'}
	</div>
</div>

<style>
	.custom-tracking-time {
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% {
			opacity: 1;
		}
		50% {
			opacity: 0.7;
		}
		100% {
			opacity: 1;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
