<script lang="ts">
	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell
	} from 'flowbite-svelte';
	import { PenOutline } from 'flowbite-svelte-icons';
	import { goto } from '$app/navigation';
	import TimeTrackingButton from './TimeTrackingButton.svelte';
	export let data = [];

	$: sortedData = [...data];

	const navigateToEditPage = (routeId) => {
		goto(`/routes/edit?id=${routeId}`);
	};
</script>

<Table striped={true} hoverable={true} class="rounded-lg bg-white shadow">
	<TableHead>
		<TableHeadCell>ID</TableHeadCell>
		<TableHeadCell>Start Location</TableHeadCell>
		<TableHeadCell>End Location</TableHeadCell>
		<TableHeadCell>Emergency Contact</TableHeadCell>
		<TableHeadCell>Known Dangers</TableHeadCell>
		<TableHeadCell>Extra Notes</TableHeadCell>
		<TableHeadCell>Actions</TableHeadCell>
	</TableHead>
	<TableBody>
		{#each sortedData as route}
			<TableBodyRow>
				<TableBodyCell>{route.id}</TableBodyCell>
				<TableBodyCell>{route.start_location_name}</TableBodyCell>
				<TableBodyCell>{route.end_location_name}</TableBodyCell>
				<TableBodyCell>{route.emergency_contact}</TableBodyCell>
				<TableBodyCell>{route.known_dangers}</TableBodyCell>
				<TableBodyCell>{route.extra_notes}</TableBodyCell>
				<TableBodyCell>
					<div class="flex items-center space-x-2">
						<button
							on:click={() => navigateToEditPage(route.id)}
							class="rounded-full p-1 text-primary-500 hover:bg-gray-100 hover:text-primary-600"
							title="Edit route"
						>
							<PenOutline class="h-5 w-5" />
						</button>
						<TimeTrackingButton
							routeId={route.id}
							routeInfo={{
								start_location_name: route.start_location_name,
								end_location_name: route.end_location_name
							}}
						/>
					</div>
				</TableBodyCell>
			</TableBodyRow>
		{/each}
	</TableBody>
</Table>
