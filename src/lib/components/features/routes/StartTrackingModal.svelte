<script lang="ts">
	import { Modal, Label, Input, Button, Select, Textarea } from 'flowbite-svelte';
	import { startTimeTracking } from '$lib/services/time-tracking.service';
	import { userProfile, keycloakClient } from '$lib/stores';
	import { setActiveRoute } from '$lib/stores/time-tracking.store';
	import { get } from 'svelte/store';
	import type { Route } from '$lib/services/routes.service';

	export let showModal: boolean;
	export let routes: Route[] = [];
	export let selectedRouteId: number | null = null;
	export let onClose: () => void;
	export let onTrackingStarted: () => void;

	let formData = {
		route_id: '',
		pilot_email: '',
		description: '',
		ride_id: ''
	};

	let errors: {
		[key: string]: string;
	} = {};

	let isSubmitting = false;

	function getActualEmail() {
		if ($userProfile && $userProfile.email) {
			return $userProfile.email;
		}

		const keycloak = get(keycloakClient);
		if (keycloak && keycloak.tokenParsed) {
			return keycloak.tokenParsed.email || '';
		}

		return '';
	}

	$: {
		if (showModal) {
			const email = getActualEmail();
			if (email) {
				formData.pilot_email = email;
			}
			if (selectedRouteId) {
				formData.route_id = selectedRouteId.toString();
			}
		}
	}

	const resetForm = () => {
		formData = {
			route_id: '',
			pilot_email: '',
			description: '',
			ride_id: ''
		};
		errors = {};
	};

	const validateForm = () => {
		errors = {};

		const routeId = selectedRouteId || formData.route_id;
		if (!routeId) {
			errors.route_id = 'Route is required';
		}

		if (!formData.pilot_email) {
			errors.pilot_email = 'Pilot email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.pilot_email)) {
			errors.pilot_email = 'Please enter a valid email address';
		}

		if (formData.ride_id && isNaN(Number(formData.ride_id))) {
			errors.ride_id = 'Ride ID must be a number';
		}

		return Object.keys(errors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) {
			return;
		}

		try {
			isSubmitting = true;
			const routeId = selectedRouteId || Number(formData.route_id);
			const result = await startTimeTracking(
				routeId,
				formData.pilot_email,
				formData.description || undefined,
				formData.ride_id ? Number(formData.ride_id) : undefined
			);

			if (result) {
				setActiveRoute(routeId);
				resetForm();
				onTrackingStarted();
			} else {
				errors.general = 'Failed to start time tracking. Please try again.';
			}
		} catch (error) {
			errors.general = 'An error occurred while starting time tracking.';
		} finally {
			isSubmitting = false;
		}
	};

	const handleClose = () => {
		resetForm();
		onClose();
	};

	const hasError = (field: string) => {
		return errors[field] !== undefined;
	};
</script>

<Modal bind:open={showModal} size="md" autoclose={false} on:close={handleClose}>
	<div class="p-4">
		<h3 class="mb-4 text-xl font-medium text-gray-900">Start Time Tracking</h3>

		{#if errors.general}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{errors.general}
			</div>
		{/if}

		<form class="space-y-6" on:submit|preventDefault={handleSubmit}>
			{#if selectedRouteId}
				{@const selectedRoute = routes.find((r) => r.id === selectedRouteId)}
				{#if selectedRoute}
					<div>
						<Label class="mb-2">Selected Route</Label>
						<div class="rounded-lg border border-gray-200 bg-gray-50 p-3">
							<div class="font-medium text-gray-900">
								{selectedRoute.external_route_id || `Route ${selectedRoute.id}`}
							</div>
							<div class="text-sm text-gray-600">
								{selectedRoute.start_location_name} → {selectedRoute.end_location_name}
							</div>
						</div>
					</div>
				{/if}
			{:else}
				<div>
					<Label for="route_id" class="mb-2">Route *</Label>
					<Select
						id="route_id"
						required
						bind:value={formData.route_id}
						class={hasError('route_id') ? 'border-red-500' : ''}
					>
						<option value="" disabled>Select a route</option>
						{#each routes as route}
							<option value={route.id}>
								{route.external_route_id || `Route ${route.id}`}: {route.start_location_name}
								→ {route.end_location_name}
							</option>
						{/each}
					</Select>
					{#if hasError('route_id')}
						<p class="mt-1 text-sm text-red-600">{errors.route_id}</p>
					{/if}
				</div>
			{/if}

			<div>
				<Label for="pilot_email" class="mb-2">Pilot Email *</Label>
				<Input
					id="pilot_email"
					type="email"
					required
					bind:value={formData.pilot_email}
					class={hasError('pilot_email') ? 'border-red-500' : ''}
					placeholder="Enter pilot email"
				/>
				{#if hasError('pilot_email')}
					<p class="mt-1 text-sm text-red-600">{errors.pilot_email}</p>
				{/if}
			</div>

			<div>
				<Label for="description" class="mb-2">Description</Label>
				<Textarea
					id="description"
					bind:value={formData.description}
					placeholder="Enter optional description for this tracking session"
					rows="3"
				/>
				<p class="mt-1 text-xs text-gray-500">Optional description for this tracking session</p>
			</div>

			<div>
				<Label for="ride_id" class="mb-2">Ride ID</Label>
				<Input
					id="ride_id"
					type="number"
					bind:value={formData.ride_id}
					class={hasError('ride_id') ? 'border-red-500' : ''}
					placeholder="Enter optional ride ID"
				/>
				{#if hasError('ride_id')}
					<p class="mt-1 text-sm text-red-600">{errors.ride_id}</p>
				{/if}
				<p class="mt-1 text-xs text-gray-500">Optional ride identifier</p>
			</div>

			<div class="flex justify-end gap-3 pt-4">
				<Button color="alternative" on:click={handleClose} disabled={isSubmitting}>Cancel</Button>
				<Button
					type="submit"
					disabled={isSubmitting}
					class="bg-green-600 hover:bg-green-700 focus:ring-green-500"
				>
					{#if isSubmitting}
						<div
							class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
						></div>
						Starting...
					{:else}
						Start Tracking
					{/if}
				</Button>
			</div>
		</form>
	</div>
</Modal>
