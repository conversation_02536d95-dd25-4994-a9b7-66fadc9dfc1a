<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		startTimeTracking,
		stopTimeTracking,
		getLastShift
	} from '$lib/services/time-tracking.service';
	import { userProfile, keycloakClient } from '$lib/stores';
	import { trackingStore, setActiveRoute } from '$lib/stores/time-tracking.store';
	import { get } from 'svelte/store';

	export let routeId: number;
	export let routeInfo: { start_location_name?: string; end_location_name?: string } = {};
	export let onStartTrackingClick: ((routeId: number) => void) | null = null;

	let isTracking = false;
	let isLoading = false;
	let errorMessage = '';
	let showStartAnimation = false;

	let trackingStartTime: Date | null = null;
	let elapsedTime = '00:00:00';
	let timer: number;
	let pilotEmail = '';
	let currentShiftId: number | null = null;

	$: storeState = $trackingStore;
	$: isDisabled =
		storeState.hasActiveTracking && storeState.activeRouteId !== routeId && !isTracking;

	let unsubscribeFromStore: () => void = () => {};

	function getActualEmail() {
		if ($userProfile && $userProfile.email) {
			return $userProfile.email;
		}

		const keycloak = get(keycloakClient);
		if (keycloak && keycloak.tokenParsed) {
			return keycloak.tokenParsed.email || '<EMAIL>';
		}

		return '<EMAIL>';
	}

	function formatTimeForDisplay(timeString: string): string {
		const [hours, minutes, seconds] = timeString.split(':').map(Number);

		if (hours > 0) {
			return `${hours}h ${minutes}m`;
		} else {
			return `${minutes}m ${seconds}s`;
		}
	}

	function startTimer() {
		if (timer) clearInterval(timer);
		timer = setInterval(updateElapsedTime, 1000);
	}

	function updateElapsedTime() {
		if (!trackingStartTime) return;

		const now = new Date();
		const diff = now.getTime() - trackingStartTime.getTime();

		const totalSeconds = Math.floor(diff / 1000);
		const hours = Math.floor(totalSeconds / 3600)
			.toString()
			.padStart(2, '0');
		const minutes = Math.floor((totalSeconds % 3600) / 60)
			.toString()
			.padStart(2, '0');
		const seconds = Math.floor(totalSeconds % 60)
			.toString()
			.padStart(2, '0');

		elapsedTime = `${hours}:${minutes}:${seconds}`;
	}

	function resetTrackingStates(updateStore = true) {
		isTracking = false;
		currentShiftId = null;
		if (updateStore) {
			if ($trackingStore.activeRouteId === routeId) {
				setActiveRoute(null);
			}
		}
		if (timer) {
			clearInterval(timer);
			timer = undefined;
		}
		errorMessage = '';
	}

	async function checkActiveTracking() {
		try {
			errorMessage = '';
			pilotEmail = getActualEmail();
			const lastShift = await getLastShift(pilotEmail);

			if (lastShift && !lastShift.stop_time) {
				setActiveRoute(lastShift.route_id);
				if (lastShift.route_id === routeId) {
					isTracking = true;
					currentShiftId = lastShift.shift_id || null;
					const startTimeStr = lastShift.start_time;
					trackingStartTime = new Date(startTimeStr + 'Z');
					updateElapsedTime();
					startTimer();
				} else {
					isTracking = false;
					currentShiftId = null;
					if (timer) clearInterval(timer);
					elapsedTime = '00:00:00';
				}
			} else {
				if ($trackingStore.activeRouteId === routeId) {
					setActiveRoute(null);
				}
				isTracking = false;
				currentShiftId = null;
				if (timer) clearInterval(timer);
				elapsedTime = '00:00:00';
			}
		} catch (error) {
			console.error('Failed to check active tracking:', error);
			errorMessage = 'Error checking status';
		}
	}

	async function toggleTracking() {
		if (isLoading || isDisabled) return;

		isLoading = true;
		errorMessage = '';

		try {
			pilotEmail = getActualEmail();
			const keycloak = get(keycloakClient);
			if (!keycloak?.token) {
				errorMessage = 'Authentication required';
				isLoading = false;
				return;
			}

			if (isTracking) {
				if (!currentShiftId) {
					const lastShift = await getLastShift(pilotEmail);

					if (lastShift && !lastShift.stop_time && lastShift.route_id === routeId) {
						currentShiftId = lastShift.id || lastShift.shift_id;
					}
				}

				if (currentShiftId) {
					const result = await stopTimeTracking(currentShiftId);

					if (result) {
						isTracking = false;
						currentShiftId = null;
						setActiveRoute(null);
						if (timer) {
							clearInterval(timer);
							timer = undefined;
						}
						elapsedTime = '00:00:00';
						trackingStartTime = null;
					} else {
						errorMessage = 'Failed to stop tracking. Please try again.';
						isLoading = false;
						return;
					}
				} else {
					errorMessage = 'No active shift found';
					isLoading = false;
					return;
				}
			} else {
				if ($trackingStore.hasActiveTracking && $trackingStore.activeRouteId !== routeId) {
					errorMessage = `Active tracking on route #${$trackingStore.activeRouteId}`;
					isLoading = false;
					return;
				}

				const lastShift = await getLastShift(pilotEmail);
				if (lastShift && !lastShift.stop_time) {
					if (lastShift.route_id !== routeId) {
						errorMessage = `Active tracking found on route #${lastShift.route_id} (backend check)`;
						setActiveRoute(lastShift.route_id);
						isLoading = false;
						return;
					} else {
						isTracking = true;
						setActiveRoute(routeId);
						trackingStartTime = new Date(lastShift.start_time + 'Z');
						updateElapsedTime();
						startTimer();
						isLoading = false;
						return;
					}
				}
				if (onStartTrackingClick) {
					isLoading = false;
					onStartTrackingClick(routeId);
					return;
				}
				const defaultDescription =
					routeInfo.start_location_name && routeInfo.end_location_name
						? `Work on route: ${routeInfo.start_location_name} ⬌ ${routeInfo.end_location_name}`
						: `Work on route #${routeId}`;

				const trackingEvent = await startTimeTracking(routeId, pilotEmail, defaultDescription);

				if (trackingEvent && trackingEvent.shift_id) {
					currentShiftId = trackingEvent.shift_id;
					isTracking = true;
					setActiveRoute(routeId);
					trackingStartTime = new Date();
					updateElapsedTime();
					startTimer();

					showStartAnimation = true;
					setTimeout(() => {
						showStartAnimation = false;
					}, 1500);
				} else {
					errorMessage = 'Failed to start tracking';
					isLoading = false;
					return;
				}
			}
		} catch (error: any) {
			errorMessage = error?.message || 'System error occurred';
			await checkActiveTracking();
		} finally {
			isLoading = false;
		}
	}

	onMount(async () => {
		await checkActiveTracking();
		unsubscribeFromStore = trackingStore.subscribe((currentState) => {
			if (isTracking && currentState.activeRouteId !== routeId) {
				isTracking = false;
				if (timer) clearInterval(timer);
				elapsedTime = '00:00:00';
			} else if (!isTracking && currentState.activeRouteId === routeId) {
				checkActiveTracking();
			}
		});
	});

	onDestroy(() => {
		if (timer) {
			clearInterval(timer);
		}
		if (unsubscribeFromStore) {
			unsubscribeFromStore();
		}
	});
</script>

<div class="time-tracker">
	<div
		class="tracker-card {isTracking ? 'active' : ''} {showStartAnimation ? 'animate-start' : ''}"
	>
		<button
			class="tracker-button {isTracking ? 'stop' : 'start'} {isDisabled ? 'disabled' : ''}"
			on:click={(e) => {
				e.preventDefault();
				e.stopPropagation();
				toggleTracking();
			}}
			disabled={isLoading || isDisabled}
			aria-label={isTracking ? 'Stop tracking' : 'Start tracking'}
			data-testid={isTracking ? 'stop-tracking-btn' : 'start-tracking-btn'}
		>
			{#if isLoading}
				<div class="loader"></div>
			{:else if isTracking}
				<div class="button-content">
					<svg class="icon" viewBox="0 0 24 24" width="14" height="14"
						><rect x="6" y="6" width="12" height="12" /></svg
					>
					<span>Stop tracking</span>
				</div>
				<div class="time">{formatTimeForDisplay(elapsedTime)}</div>
			{:else}
				<div class="button-content">
					<svg class="icon" viewBox="0 0 24 24" width="14" height="14"
						><polygon points="6,4 20,12 6,20" /></svg
					>
					<span>Start time tracking</span>
				</div>
			{/if}
		</button>
	</div>
</div>

<style>
	.time-tracker {
		font-family:
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			'Segoe UI',
			Roboto,
			sans-serif;
		width: 180px;
		font-size: 13px;
	}

	.tracker-card {
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
		overflow: hidden;
		transition: box-shadow 0.2s ease;
	}

	.tracker-card.active {
		box-shadow:
			0 0 0 2px rgba(72, 168, 81, 0.3),
			0 2px 5px rgba(0, 0, 0, 0.15);
	}

	.tracker-card.animate-start {
		animation: start-glow 1.5s ease;
	}

	.tracker-button {
		width: 100%;
		height: 38px;
		border: none;
		font-weight: 600;
		font-size: 13px;
		cursor: pointer;
		transition: background-color 0.15s ease;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10px 12px;
	}

	.tracker-button.disabled {
		opacity: 0.6;
		cursor: not-allowed;
		background-color: #6b7280;
	}

	.button-content {
		display: flex;
		align-items: center;
		gap: 6px;
	}

	.tracker-button.start {
		background-color: #48a851;
		color: white;
	}

	.tracker-button.start:hover:not(:disabled) {
		background-color: #3d9046;
		transform: none;
	}

	.tracker-button.stop {
		background-color: #ef4444;
		color: white;
	}

	.tracker-button.stop:hover:not(:disabled) {
		background-color: #dc3030;
		transform: none;
	}

	.tracker-button:disabled {
		opacity: 0.7;
		cursor: not-allowed;
	}

	.tracker-button:focus {
		outline: none;
		box-shadow: 0 0 0 2px rgba(72, 168, 81, 0.3);
	}

	.icon {
		fill: currentColor;
		flex-shrink: 0;
	}

	.time {
		background-color: rgba(255, 255, 255, 0.2);
		padding: 2px 4px;
		border-radius: 4px;
		font-size: 11px;
		font-weight: 500;
		min-width: 40px;
		text-align: center;
	}

	.loader {
		width: 16px;
		height: 16px;
		border: 2px solid rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		border-top-color: white;
		animation: spin 1s linear infinite;
		margin: 0 auto;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes start-glow {
		0% {
			box-shadow:
				0 0 0 1px rgba(72, 168, 81, 0.3),
				0 1px 3px rgba(0, 0, 0, 0.1);
		}
		50% {
			box-shadow:
				0 0 10px 2px rgba(72, 168, 81, 0.5),
				0 1px 3px rgba(0, 0, 0, 0.1);
		}
		100% {
			box-shadow:
				0 0 0 1px rgba(72, 168, 81, 0.3),
				0 1px 3px rgba(0, 0, 0, 0.1);
		}
	}
</style>
