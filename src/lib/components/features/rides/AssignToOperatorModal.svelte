<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Modal, Button, Label, Input } from 'flowbite-svelte';
	import type { UserData } from '$lib/services/users.service';

	export let rideId: number;
	export let operators: UserData[] = [];

	let selectedOperatorEmail = '';
	let searchTerm = '';
	let showSuggestions = false;
	let filteredOperators: UserData[] = [];
	let isAssigning = false;
	let open = true;

	const dispatch = createEventDispatcher();

	$: {
		if (searchTerm.length > 0) {
			filteredOperators = operators
				.filter(
					(operator) =>
						operator.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
						operator.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
						operator.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
						operator.username?.toLowerCase().includes(searchTerm.toLowerCase())
				)
				.slice(0, 10); // Limit to 10 suggestions
			showSuggestions = filteredOperators.length > 0;
		} else {
			filteredOperators = [];
			showSuggestions = false;
		}
	}

	function handleInputFocus() {
		if (searchTerm.length > 0) {
			showSuggestions = filteredOperators.length > 0;
		}
	}

	function handleInputBlur() {
		setTimeout(() => {
			showSuggestions = false;
		}, 200);
	}

	function selectOperator(operator: UserData) {
		selectedOperatorEmail = operator.email || '';
		searchTerm = operator.email || '';
		showSuggestions = false;
	}

	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;
		searchTerm = target.value;
		selectedOperatorEmail = target.value;
	}

	async function handleAssign() {
		if (!selectedOperatorEmail.trim()) {
			return;
		}

		try {
			isAssigning = true;

			dispatch('assigned', {
				operatorEmail: selectedOperatorEmail
			});
		} catch (error) {
			console.error('Error assigning operator:', error);
		} finally {
			isAssigning = false;
		}
	}

	function handleClose() {
		dispatch('close');
	}

	function getDisplayName(operator: UserData): string {
		if (operator.email) return operator.email;
		if (operator.firstName && operator.lastName) {
			return `${operator.firstName} ${operator.lastName}`;
		}
		return operator.username || 'Unknown';
	}
</script>

<Modal bind:open size="md" autoclose={false} dismissable={false} class="w-full">
	<div class="flex items-center justify-between rounded-t border-b p-4 md:p-5">
		<h3 class="text-xl font-semibold text-gray-900">
			Assign Operator to Ride #{rideId}
		</h3>
		<button
			type="button"
			class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900"
			on:click={handleClose}
		>
			<svg
				class="h-3 w-3"
				aria-hidden="true"
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 14 14"
			>
				<path
					stroke="currentColor"
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
				/>
			</svg>
			<span class="sr-only">Close modal</span>
		</button>
	</div>

	<div class="space-y-4 p-4 md:p-5">
		<div class="relative">
			<Label for="operator-search" class="mb-2">Operator Email</Label>
			<Input
				id="operator-search"
				type="text"
				placeholder="Start typing operator email..."
				bind:value={searchTerm}
				on:input={handleSearchInput}
				on:focus={handleInputFocus}
				on:blur={handleInputBlur}
				autocomplete="off"
				class="w-full"
			/>

			{#if showSuggestions}
				<div class="suggestions-dropdown">
					{#each filteredOperators as operator (operator.id)}
						<button type="button" class="suggestion-item" on:click={() => selectOperator(operator)}>
							<div class="suggestion-content">
								<div class="suggestion-primary">{getDisplayName(operator)}</div>
								{#if operator.email && operator.firstName && operator.lastName}
									<div class="suggestion-secondary">{operator.firstName} {operator.lastName}</div>
								{/if}
							</div>
						</button>
					{/each}
				</div>
			{/if}
		</div>

		<div class="text-sm text-gray-500">
			<strong>Note:</strong> Select an operator from the suggestions or type a valid email address.
		</div>
	</div>

	<div class="flex items-center rounded-b border-t border-gray-200 p-4 md:p-5">
		<Button
			color="blue"
			disabled={!selectedOperatorEmail.trim() || isAssigning}
			on:click={handleAssign}
			class="mr-2"
		>
			{#if isAssigning}
				Assigning...
			{:else}
				Assign Operator
			{/if}
		</Button>
	</div>
</Modal>

<style>
	.suggestions-dropdown {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		z-index: 50;
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
		max-height: 200px;
		overflow-y: auto;
		margin-top: 4px;
	}

	.suggestion-item {
		display: block;
		width: 100%;
		padding: 12px 16px;
		text-align: left;
		background: none;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.suggestion-item:hover {
		background-color: #f9fafb;
	}

	.suggestion-content {
		display: flex;
		flex-direction: column;
		gap: 2px;
	}

	.suggestion-primary {
		font-size: 14px;
		color: #374151;
		font-weight: 500;
	}

	.suggestion-secondary {
		font-size: 12px;
		color: #6b7280;
	}
</style>
