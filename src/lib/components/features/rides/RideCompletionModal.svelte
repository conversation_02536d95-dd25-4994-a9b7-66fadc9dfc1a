<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Modal, Button, Label } from 'flowbite-svelte';
	import PackageInfoSection from './PackageInfoSection.svelte';

	export let rideId: number;
	export let error = ''; // Error message from parent

	let isUpdating = false;
	let open = true;

	// Package delivery state
	let hasPackage = false;
	let packageDescription = '';
	let packageInfoSection: PackageInfoSection;

	const dispatch = createEventDispatcher();

	function handleClose() {
		dispatch('close');
	}

	function handleCancel() {
		dispatch('cancel');
	}

	async function handleConfirm() {
		// Validate package information using the shared component
		if (!packageInfoSection.validate()) {
			return;
		}

		try {
			isUpdating = true;
			dispatch('confirm', {
				hasPackage,
				packageDescription: hasPackage ? packageDescription : ''
			});
		} catch (error) {
			// Error confirming ride completion
		} finally {
			isUpdating = false;
		}
	}

	function handlePackageChange(event: CustomEvent) {
		hasPackage = event.detail.hasPackage;
		packageDescription = event.detail.packageDescription;
	}
</script>

<Modal bind:open size="lg" autoclose={false} dismissable={false} class="mx-auto w-full max-w-lg">
	<div class="flex items-center justify-between rounded-t border-b px-4 py-3 md:px-6">
		<h3 class="text-lg font-semibold text-gray-900">
			Complete Ride #{rideId}
		</h3>
		<button
			type="button"
			class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900"
			on:click={handleClose}
			aria-label="Close modal"
		>
			<svg
				class="h-3 w-3"
				aria-hidden="true"
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 14 14"
			>
				<path
					stroke="currentColor"
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
				/>
			</svg>
		</button>
	</div>

	<div class="px-4 py-3 md:px-6">
		<!-- Error Display -->
		{#if error}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{error}
			</div>
		{/if}

		<div class="mb-2">
			<Label class="mb-1 text-base font-medium text-gray-900">
				Did this ride deliver a package?
			</Label>
		</div>

		<!-- Package Information Section -->
		<PackageInfoSection
			bind:this={packageInfoSection}
			bind:hasPackage
			bind:packageDescription
			checkboxLabel="Ride carried package"
			defaultDescription="X samples"
			on:change={handlePackageChange}
		/>
	</div>

	<div
		class="flex items-center justify-end space-x-2 rounded-b border-t border-gray-200 px-4 py-2 md:px-6"
	>
		<Button color="alternative" size="sm" on:click={handleCancel} disabled={isUpdating}>
			Cancel
		</Button>
		<Button
			color="primary"
			size="sm"
			on:click={handleConfirm}
			disabled={isUpdating || !packageInfoSection?.isValid()}
		>
			{#if isUpdating}
				Updating...
			{:else}
				Complete Ride
			{/if}
		</Button>
	</div>
</Modal>
