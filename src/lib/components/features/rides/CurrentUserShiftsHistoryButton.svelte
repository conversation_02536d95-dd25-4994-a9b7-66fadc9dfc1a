<script lang="ts">
	import { Button } from 'flowbite-svelte';
	import { ClockOutline } from 'flowbite-svelte-icons';
	import { userProfile, keycloakClient } from '$lib/stores';
	import { get } from 'svelte/store';
	import ShiftsHistoryModal from './ShiftsHistoryModal.svelte';

	let showModal = false;
	let pilotEmail = '';
	let loading = false;
	let error = '';

	function getCurrentUserEmail(): string {
		if ($userProfile && $userProfile.email) {
			return $userProfile.email;
		}

		const keycloak = get(keycloakClient);
		if (keycloak && keycloak.tokenParsed && keycloak.tokenParsed.email) {
			return keycloak.tokenParsed.email;
		}

		throw new Error('Unable to get current user email');
	}

	function openModal() {
		try {
			loading = true;
			error = '';

			pilotEmail = getCurrentUserEmail();
			showModal = true;
		} catch (err) {
			console.error('Error opening history modal:', err);
			error = err instanceof Error ? err.message : 'Failed to open history modal';
		} finally {
			loading = false;
		}
	}

	function closeModal() {
		showModal = false;
		pilotEmail = '';
		error = '';
	}
</script>

{#if error}
	<Button color="red" size="sm" disabled title={error}>
		<ClockOutline class="mr-2 h-4 w-4" />
		Error
	</Button>
{:else}
	<Button
		color="blue"
		size="sm"
		on:click={openModal}
		disabled={loading}
		title="View my shift history"
	>
		<ClockOutline class="mr-2 h-4 w-4" />
		{loading ? 'Loading...' : 'My Shifts History'}
	</Button>
{/if}

{#if showModal}
	<ShiftsHistoryModal bind:showModal {pilotEmail} onClose={closeModal} />
{/if}
