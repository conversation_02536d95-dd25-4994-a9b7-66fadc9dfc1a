<script lang="ts">
	import { onMount } from 'svelte';
	import { fetchRoutes, type Route } from '$lib/services/routes.service';
	import {
		Button,
		Input,
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Spinner,
		Datepicker,
		Label,
		Select,
		ButtonGroup
	} from 'flowbite-svelte';
	import { ChevronLeftOutline, ChevronRightOutline } from 'flowbite-svelte-icons';

	import RideTimeTrackingButton from './RideTimeTrackingButton.svelte';
	import RideStatusDropdown from './RideStatusDropdown.svelte';
	import RideActionsDropdown from './RideActionsDropdown.svelte';
	import CurrentUserShiftsHistoryButton from './CurrentUserShiftsHistoryButton.svelte';
	import { goto } from '$app/navigation';
	import { get } from 'svelte/store';
	import { keycloakClient, userProfile } from '$lib/stores';
	import { fetchRides, createRide, type Ride } from '$lib/services/rides.service';
	import { fetchUsers, type UserData } from '$lib/services/users.service';
	import { fetchRideStatuses, type RideStatus } from '$lib/services/ride-status.service';
	import { fetchCancelReasons, type CancelReason } from '$lib/services/cancel-reasons.service';
	import { fetchGliders, type Glider } from '$lib/services/gliders.service';
	import { fetchLocations, type Location } from '$lib/services/locations.service';
	import { fetchCustomers, type Customer } from '$lib/services/customers.service';
	import { exportRidesCSV, type RidesExportParams } from '$lib/services/export.service';
	import { formatDate, formatTime, formatTimeUTC, formatDateUTC } from '$lib/utils/datetime';

	let rides: Ride[] = [];
	let loading = true;
	let error: string | null = null;

	let startDateTime: string | null = null;
	let endDateTime: string | null = null;
	let selectedStatus: number | null = null;
	let operatorSearchTerm: string = '';
	let customerSearchTerm: string = '';
	let routeSearchTerm: string = '';

	let showOperatorSuggestions = false;
	let showCustomerSuggestions = false;
	let showRouteSuggestions = false;
	let filteredOperators: UserData[] = [];
	let filteredCustomers: Customer[] = [];
	let filteredRoutes: Route[] = [];

	let selectedOperatorId: string | null = null;
	let selectedCustomerId: number | null = null;
	let selectedRouteId: number | null = null;

	let operators: UserData[] = [];
	let customers: Customer[] = [];
	let routes: Route[] = [];

	let currentPage = 1;
	let pageSize = 50; // Default to 50 rows per page as preferred
	let totalRides = 0;
	let totalPages = 0;
	let previousPageSize = pageSize;

	const pageSizeOptions = [10, 20, 50];

	$: if (pageSize !== previousPageSize) {
		previousPageSize = pageSize;
		currentPage = 1; // Reset to first page when changing page size
		loadRides();
	}

	let rideStatuses: RideStatus[] = [];

	let cancelReasons: CancelReason[] = [];

	let gliders: Glider[] = [];

	let locations: Location[] = [];

	let globalErrorMessage = '';
	let globalErrorTimeout: number;

	let exportLoading = false;
	let activeShiftInfo: { routeId: number; operatorEmail: string } | null = null;

	async function loadRides() {
		try {
			loading = true;
			error = null;

			const skip = (currentPage - 1) * pageSize;

			const filterParams = {
				skip,
				limit: pageSize,
				operator_id: selectedOperatorId,
				ride_status_id: selectedStatus,
				route_id: selectedRouteId,
				customer_id: selectedCustomerId,
				start_time: startDateTime ? new Date(startDateTime).toISOString() : null,
				end_time: endDateTime ? new Date(endDateTime).toISOString() : null
			};

			const ridesData = await fetchRides(filterParams);

			rides = ridesData;

			if (ridesData.length < pageSize) {
				totalRides = skip + ridesData.length;
			} else {
				totalRides = Math.max(totalRides, skip + ridesData.length + 1);
			}

			totalPages = Math.ceil(totalRides / pageSize);
		} catch (err) {
			console.error('Error loading rides:', err);
			error = err instanceof Error ? err.message : 'Unknown error loading rides';
		} finally {
			loading = false;
		}
	}

	onMount(async () => {
		const keycloak = get(keycloakClient);

		try {
			loading = true;

			const [
				statusesData,
				cancelReasonsData,
				glidersData,
				locationsData,
				customersData,
				routesData,
				operatorsData
			] = await Promise.all([
				fetchRideStatuses().catch(() => []),
				fetchCancelReasons().catch(() => []),
				fetchGliders().catch(() => []),
				fetchLocations().catch(() => []),
				fetchCustomers(0, 1000).catch(() => []), // Get more customers for filtering
				fetchRoutes().catch(() => []),
				fetchUsers().catch(() => {
					console.warn('Could not load operators - user may not have admin permissions');
					return [];
				})
			]);

			rideStatuses = statusesData;
			cancelReasons = cancelReasonsData;
			gliders = glidersData;
			locations = locationsData;
			customers = customersData;
			routes = routesData;
			operators = operatorsData;

			await loadRides();
		} catch (err) {
			console.error('Error during initialization:', err);
			error = err instanceof Error ? err.message : 'Unknown error during initialization';
		}
	});

	const navigateToEditPage = (rideId) => {
		goto(`/rides/edit?id=${rideId}`);
	};

	function applyFilters() {
		currentPage = 1; // Reset to first page when applying filters
		loadRides();
	}

	function clearFilters() {
		startDateTime = null;
		endDateTime = null;
		selectedStatus = null;
		operatorSearchTerm = '';
		customerSearchTerm = '';
		routeSearchTerm = '';
		selectedOperatorId = null;
		selectedCustomerId = null;
		selectedRouteId = null;
		showOperatorSuggestions = false;
		showCustomerSuggestions = false;
		showRouteSuggestions = false;
		applyFilters();
	}

	function filterOperators(searchTerm: string) {
		if (!searchTerm.trim()) {
			filteredOperators = [];
			return;
		}

		const term = searchTerm.toLowerCase();
		filteredOperators = operators
			.filter(
				(operator) =>
					operator.email?.toLowerCase().includes(term) ||
					operator.firstName?.toLowerCase().includes(term) ||
					operator.lastName?.toLowerCase().includes(term) ||
					operator.username?.toLowerCase().includes(term)
			)
			.slice(0, 10);
	}

	function handleOperatorInput() {
		filterOperators(operatorSearchTerm);
		showOperatorSuggestions = filteredOperators.length > 0;
	}

	function selectOperator(operator: UserData) {
		operatorSearchTerm = operator.email || '';
		selectedOperatorId = operator.id;
		showOperatorSuggestions = false;
		applyFilters();
	}

	function filterCustomers(searchTerm: string) {
		if (!searchTerm.trim()) {
			filteredCustomers = [];
			return;
		}

		const term = searchTerm.toLowerCase();
		filteredCustomers = customers
			.filter((customer) => customer.name?.toLowerCase().includes(term))
			.slice(0, 10);
	}

	function handleCustomerInput() {
		filterCustomers(customerSearchTerm);
		showCustomerSuggestions = filteredCustomers.length > 0;
	}

	function selectCustomer(customer: Customer) {
		customerSearchTerm = customer.name || '';
		selectedCustomerId = customer.id;
		showCustomerSuggestions = false;
		applyFilters();
	}

	function filterRoutes(searchTerm: string) {
		if (!searchTerm.trim()) {
			filteredRoutes = [];
			return;
		}

		const term = searchTerm.toLowerCase();
		filteredRoutes = routes
			.filter(
				(route) =>
					route.start_location_name?.toLowerCase().includes(term) ||
					route.end_location_name?.toLowerCase().includes(term) ||
					route.external_route_id?.toLowerCase().includes(term)
			)
			.slice(0, 10);
	}

	function handleRouteInput() {
		filterRoutes(routeSearchTerm);
		showRouteSuggestions = filteredRoutes.length > 0;
	}

	function selectRoute(route: Route) {
		routeSearchTerm = `${route.start_location_name} → ${route.end_location_name}`;
		selectedRouteId = route.id;
		showRouteSuggestions = false;
		applyFilters();
	}

	async function goToPage(page: number) {
		if (page < 1 || page > totalPages || page === currentPage) return;
		currentPage = page;
		await loadRides();
	}

	function getPageNumbers(): number[] {
		const pages: number[] = [];
		const maxVisiblePages = 5;

		if (totalPages <= maxVisiblePages) {
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			const start = Math.max(1, currentPage - 2);
			const end = Math.min(totalPages, start + maxVisiblePages - 1);

			for (let i = start; i <= end; i++) {
				pages.push(i);
			}
		}

		return pages;
	}

	function handleRideStatusChange(rideId: number, newStatusId: number) {
		rides = rides.map((ride) =>
			ride.id === rideId ? { ...ride, ride_status_id: newStatusId } : ride
		);
	}

	function handleRidePackageChange(
		rideId: number,
		hasPackage: boolean,
		packageDescription: string
	) {
		rides = rides.map((ride) =>
			ride.id === rideId
				? { ...ride, has_package: hasPackage, package_description: packageDescription }
				: ride
		);
	}

	function handleRideCancelReasonChange(rideId: number, cancelReasonId: number | null) {
		rides = rides.map((ride) =>
			ride.id === rideId ? { ...ride, cancel_reason_id: cancelReasonId } : ride
		);
	}

	function getOperatorEmail(operatorId: string | number): string {
		if (!operatorId || !operators.length) return '-';

		const operator = operators.find((user) => user.id === String(operatorId));
		return operator?.email || '-';
	}

	function getCancelReasonName(cancelReasonId: number | null): string {
		if (!cancelReasonId || !cancelReasons.length) return '-';

		const cancelReason = cancelReasons.find((reason) => reason.id === cancelReasonId);
		return cancelReason?.name || String(cancelReasonId) || '-';
	}

	function getLocationName(locationId: number | string | null): string {
		if (!locationId || !locations.length) return String(locationId) || '-';

		const location = locations.find((loc) => loc.id === Number(locationId));
		return location?.name || String(locationId) || '-';
	}

	function showGlobalError(message: string) {
		if (globalErrorTimeout) {
			clearTimeout(globalErrorTimeout);
		}

		globalErrorMessage = message;

		globalErrorTimeout = setTimeout(() => {
			globalErrorMessage = '';
		}, 5000);
	}

	function clearGlobalError() {
		if (globalErrorTimeout) {
			clearTimeout(globalErrorTimeout);
		}
		globalErrorMessage = '';
	}

	function handleOperatorAssigned(event: CustomEvent) {
		const { rideId, operatorEmail } = event.detail;
		showGlobalError(`Operator assignment not yet implemented for ride ${rideId}`);
	}

	function handleGliderAssigned(event: CustomEvent) {
		const { rideId, gliderName } = event.detail;
		showGlobalError(`Glider assignment not yet implemented for ride ${rideId}`);
	}

	function handleRideDeleted(event: CustomEvent) {
		const { rideId } = event.detail;

		rides = rides.filter((ride) => ride.id !== rideId);

		totalRides = Math.max(0, totalRides - 1);
		totalPages = Math.ceil(totalRides / pageSize);

		if (rides.length === 0 && currentPage > 1) {
			currentPage = currentPage - 1;
			loadRides();
		}

		showGlobalError(`Ride ${rideId} has been deleted successfully.`);
	}

	async function handleExportCSV() {
		try {
			exportLoading = true;

			const exportParams: RidesExportParams = {
				operator_id: selectedOperatorId || undefined,
				ride_status_id: selectedStatus || undefined,
				route_id: selectedRouteId || undefined,
				customer_id: selectedCustomerId || undefined,
				start_time: startDateTime ? new Date(startDateTime).toISOString() : undefined,
				end_time: endDateTime ? new Date(endDateTime).toISOString() : undefined
			};

			await exportRidesCSV(exportParams);

			showGlobalError('CSV export completed successfully!');
		} catch (error) {
			console.error('Export failed:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error during export';
			showGlobalError(`Export failed: ${errorMessage}`);
		} finally {
			exportLoading = false;
		}
	}
</script>

<div class="flex min-h-screen flex-col py-8">
	<div class="w-full flex-1 px-6">
		<div class="mb-6 flex items-center justify-between">
			<div>
				<h2 class="text-2xl font-bold">Rides Page</h2>
				<p class="text-gray-600">Manage scheduled flights and track time spent on each ride</p>
			</div>
			<div class="flex items-center gap-3">
				<CurrentUserShiftsHistoryButton />
				<Button
					color="green"
					on:click={handleExportCSV}
					disabled={exportLoading}
					class="flex items-center gap-2"
				>
					{#if exportLoading}
						<Spinner size="4" />
						Exporting...
					{:else}
						Export to CSV
					{/if}
				</Button>
			</div>
		</div>

		<!-- Global Error Message -->
		{#if globalErrorMessage}
			<div class="global-error-message mb-4">
				<span class="error-text">{globalErrorMessage}</span>
				<button class="clear-error" on:click={clearGlobalError}>×</button>
			</div>
		{/if}

		<!-- Filters Panel -->
		<div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4">
			<div class="mb-4 flex items-center justify-between">
				<h3 class="text-lg font-medium text-gray-900">Filters</h3>
				<Button size="xs" color="alternative" on:click={clearFilters}>Clear All</Button>
			</div>

			<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
				<!-- Date Time Range -->
				<div>
					<Label for="start-datetime" class="mb-2">Start Date & Time</Label>
					<Input
						id="start-datetime"
						type="datetime-local"
						bind:value={startDateTime}
						on:change={applyFilters}
						class="h-10"
					/>
				</div>
				<div>
					<Label for="end-datetime" class="mb-2">End Date & Time</Label>
					<Input
						id="end-datetime"
						type="datetime-local"
						bind:value={endDateTime}
						on:change={applyFilters}
						class="h-10"
					/>
				</div>

				<!-- Status Filter -->
				<div>
					<Label for="status" class="mb-2">Ride Status</Label>
					<Select id="status" bind:value={selectedStatus} on:change={applyFilters} class="h-10">
						<option value={null}>All statuses</option>
						{#each rideStatuses as status}
							<option value={status.id}>{status.name}</option>
						{/each}
					</Select>
				</div>
			</div>

			<!-- Second row for suggestion fields -->
			<div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-3">
				<!-- Operator Filter -->
				<div class="relative">
					<Label for="operator-search" class="mb-2">Operator Email</Label>
					<Input
						id="operator-search"
						bind:value={operatorSearchTerm}
						on:input={handleOperatorInput}
						on:focus={handleOperatorInput}
						placeholder="Start typing operator email..."
						autocomplete="off"
						class="h-10"
					/>

					{#if showOperatorSuggestions && filteredOperators.length > 0}
						<div class="suggestions-dropdown">
							{#each filteredOperators as operator}
								<button
									type="button"
									class="suggestion-item"
									on:click={() => selectOperator(operator)}
								>
									<div class="suggestion-primary">{operator.email}</div>
									<div class="suggestion-secondary">
										{operator.firstName}
										{operator.lastName}
									</div>
								</button>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Customer Filter -->
				<div class="relative">
					<Label for="customer-search" class="mb-2">Customer Name</Label>
					<Input
						id="customer-search"
						bind:value={customerSearchTerm}
						on:input={handleCustomerInput}
						on:focus={handleCustomerInput}
						placeholder="Start typing customer name..."
						autocomplete="off"
						class="h-10"
					/>

					{#if showCustomerSuggestions && filteredCustomers.length > 0}
						<div class="suggestions-dropdown">
							{#each filteredCustomers as customer}
								<button
									type="button"
									class="suggestion-item"
									on:click={() => selectCustomer(customer)}
								>
									<div class="suggestion-primary">{customer.name}</div>
									<div class="suggestion-secondary">
										ID: {customer.id}
									</div>
								</button>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Route Filter -->
				<div class="relative">
					<Label for="route-search" class="mb-2">Route</Label>
					<Input
						id="route-search"
						bind:value={routeSearchTerm}
						on:input={handleRouteInput}
						on:focus={handleRouteInput}
						placeholder="Start typing route..."
						autocomplete="off"
						class="h-10"
					/>

					{#if showRouteSuggestions && filteredRoutes.length > 0}
						<div class="suggestions-dropdown">
							{#each filteredRoutes as route}
								<button type="button" class="suggestion-item" on:click={() => selectRoute(route)}>
									<div class="suggestion-primary">
										{route.start_location_name} → {route.end_location_name}
									</div>
									<div class="suggestion-secondary">
										ID: {route.id} | External: {route.external_route_id}
									</div>
								</button>
							{/each}
						</div>
					{/if}
				</div>
			</div>
		</div>

		<div class="rounded-lg bg-white py-4 shadow">
			{#if loading}
				<div class="flex flex-col items-center justify-center py-12">
					<Spinner size="8" class="common-spinner mb-4" />
					<p class="text-gray-500">Loading rides data...</p>
				</div>
			{:else if error}
				<div
					class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
					role="alert"
				>
					<p>{error}</p>
				</div>
			{:else if rides.length === 0}
				<div
					class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700"
					role="alert"
				>
					<p>No rides available</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<Table striped={true} hoverable={true} class="w-full rounded-lg bg-white shadow">
						<TableHead>
							<TableHeadCell class="w-12 px-2">ID</TableHeadCell>
							<TableHeadCell class="w-20 px-2">Date</TableHeadCell>
							<TableHeadCell class="w-16 px-2">Dep. Time</TableHeadCell>
							<TableHeadCell class="w-32 px-2">Start Location</TableHeadCell>
							<TableHeadCell class="w-20 px-2">Arr. Date</TableHeadCell>
							<TableHeadCell class="w-16 px-2">Arr. Time</TableHeadCell>
							<TableHeadCell class="w-32 px-2">End Location</TableHeadCell>
							<TableHeadCell class="w-16 px-2">Route ID</TableHeadCell>
							<TableHeadCell class="w-20 px-2">Drone</TableHeadCell>
							<TableHeadCell class="w-32 px-2">Operator</TableHeadCell>
							<TableHeadCell class="w-24 px-2">Status</TableHeadCell>
							<TableHeadCell class="px-2" style="width: 120px; max-width: 120px;"
								>Cancel Reason</TableHeadCell
							>
							<TableHeadCell class="w-16 px-2">Package</TableHeadCell>
							<TableHeadCell class="w-28 px-2">Time Tracking</TableHeadCell>
							<TableHeadCell class="w-20 px-2">Edit</TableHeadCell>
						</TableHead>
						<TableBody class="divide-y">
							{#each rides as ride (ride.id)}
								<TableBodyRow>
									<TableBodyCell class="px-2 text-sm">{ride.id}</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700"
										>{formatDate(ride.departure_time)}</TableBodyCell
									>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div class="flex flex-col">
											<span>{formatTime(ride.departure_time)}</span>
											<span class="text-xs text-gray-500"
												>{formatTimeUTC(ride.departure_time)} UTC</span
											>
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div class="max-w-32 truncate" title={getLocationName(ride.from_location)}>
											{getLocationName(ride.from_location)}
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700"
										>{formatDate(ride.arrival_time)}</TableBodyCell
									>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div class="flex flex-col">
											<span>{formatTime(ride.arrival_time)}</span>
											<span class="text-xs text-gray-500"
												>{formatTimeUTC(ride.arrival_time)} UTC</span
											>
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div class="max-w-32 truncate" title={getLocationName(ride.to_location)}>
											{getLocationName(ride.to_location)}
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700"
										>{ride.route_id || '-'}</TableBodyCell
									>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div
											class="max-w-20 truncate"
											title={ride.glider_name || ride.glider_id || '-'}
										>
											{ride.glider_name || ride.glider_id || '-'}
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<div class="max-w-32 truncate" title={getOperatorEmail(ride.operator_id)}>
											{getOperatorEmail(ride.operator_id)}
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<RideStatusDropdown
											rideId={ride.id}
											currentStatusId={ride.ride_status_id}
											{rideStatuses}
											{cancelReasons}
											onStatusChange={(newStatusId) => handleRideStatusChange(ride.id, newStatusId)}
											onCancelReasonChange={(cancelReasonId) =>
												handleRideCancelReasonChange(ride.id, cancelReasonId)}
											onPackageChange={(hasPackage, packageDescription) =>
												handleRidePackageChange(ride.id, hasPackage, packageDescription)}
										/>
									</TableBodyCell>
									<TableBodyCell
										class="px-2 text-sm font-medium text-gray-700"
										style="width: 120px; max-width: 120px;"
									>
										<div
											class="truncate"
											style="width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
											title={getCancelReasonName(ride.cancel_reason_id)}
										>
											{getCancelReasonName(ride.cancel_reason_id)}
										</div>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-center font-medium text-gray-700">
										{#if ride.has_package}
											<span class="text-lg text-green-600">✓</span>
										{:else}
											<span class="text-lg text-red-600">✗</span>
										{/if}
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<RideTimeTrackingButton
											rideId={ride.id}
											trackingRouteId={ride.route_id}
											operatorId={$userProfile.id}
											{operators}
											{activeShiftInfo}
											onError={showGlobalError}
											onTrackingStateChange={null}
										/>
									</TableBodyCell>
									<TableBodyCell class="px-2 text-sm font-medium text-gray-700">
										<RideActionsDropdown
											rideId={ride.id}
											{operators}
											{gliders}
											{locations}
											rideData={ride}
											on:operatorAssigned={handleOperatorAssigned}
											on:gliderAssigned={handleGliderAssigned}
											on:rideDeleted={handleRideDeleted}
										/>
									</TableBodyCell>
								</TableBodyRow>
							{/each}
						</TableBody>
					</Table>
				</div>

				<!-- Pagination Controls -->
				{#if !loading && rides.length > 0}
					<div class="mt-6 flex flex-col items-center justify-between gap-4 sm:flex-row">
						<!-- Page Size Selector -->
						<div class="flex items-center gap-2">
							<Label for="page-size" class="text-sm font-medium text-gray-700">Rows per page:</Label
							>
							<Select id="page-size" bind:value={pageSize} class="w-20">
								{#each pageSizeOptions as size}
									<option value={size}>{size}</option>
								{/each}
							</Select>
						</div>

						<!-- Pagination Info -->
						<div class="text-sm text-gray-700">
							Showing {(currentPage - 1) * pageSize + 1} to {Math.min(
								currentPage * pageSize,
								totalRides
							)} of {totalRides} rides
						</div>

						<!-- Page Navigation -->
						<div class="flex items-center gap-2">
							<!-- Previous Button -->
							<Button
								size="sm"
								color="alternative"
								disabled={currentPage === 1 || loading}
								on:click={() => goToPage(currentPage - 1)}
							>
								<ChevronLeftOutline class="h-4 w-4" />
								Previous
							</Button>

							<!-- Page Numbers -->
							<ButtonGroup>
								{#each getPageNumbers() as pageNum}
									<Button
										size="sm"
										color={pageNum === currentPage ? 'primary' : 'alternative'}
										disabled={loading}
										on:click={() => goToPage(pageNum)}
									>
										{pageNum}
									</Button>
								{/each}
							</ButtonGroup>

							<!-- Next Button -->
							<Button
								size="sm"
								color="alternative"
								disabled={currentPage === totalPages || loading}
								on:click={() => goToPage(currentPage + 1)}
							>
								Next
								<ChevronRightOutline class="h-4 w-4" />
							</Button>
						</div>
					</div>
				{/if}
			{/if}
		</div>
	</div>
</div>

<style>
	:global(.routes-table) {
		@apply rounded-lg bg-white p-4 shadow;
	}

	:global(.time-tracking-wrapper) {
		transition: all 0.2s ease-in-out;
	}

	:global(.time-tracking-wrapper:hover) {
		transform: translateY(-1px);
	}

	.global-error-message {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;
		background-color: #fef2f2;
		border: 1px solid #fecaca;
		border-radius: 8px;
		color: #dc2626;
		font-size: 14px;
		font-weight: 500;
		width: fit-content;
		min-width: 300px;
		max-width: 600px;
	}

	.error-text {
		flex: 1;
	}

	.clear-error {
		background: none;
		border: none;
		cursor: pointer;
		font-size: 16px;
		font-weight: bold;
		padding: 0;
		margin-left: 12px;
		opacity: 0.7;
		color: #dc2626;
	}

	.clear-error:hover {
		opacity: 1;
	}

	.suggestions-dropdown {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		z-index: 50;
		max-height: 200px;
		overflow-y: auto;
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
		margin-top: 4px;
	}

	.suggestion-item {
		display: block;
		width: 100%;
		padding: 12px 16px;
		text-align: left;
		background: none;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
		border-bottom: 1px solid #f3f4f6;
	}

	.suggestion-item:hover {
		background-color: #f9fafb;
	}

	.suggestion-item:last-child {
		border-bottom: none;
	}

	.suggestion-primary {
		font-weight: 500;
		color: #374151;
		font-size: 14px;
	}

	.suggestion-secondary {
		font-size: 12px;
		color: #6b7280;
		margin-top: 2px;
	}
</style>
