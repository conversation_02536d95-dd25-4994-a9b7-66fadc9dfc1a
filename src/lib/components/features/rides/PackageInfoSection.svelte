<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Label, Textarea, Checkbox } from 'flowbite-svelte';

	export let hasPackage = false;
	export let packageDescription = '';
	export let validationError = '';
	export let checkboxLabel = 'Ride carried package';
	export let defaultDescription = 'X samples';

	const dispatch = createEventDispatcher();

	function handlePackageChange() {
		// Clear validation error when package state changes
		validationError = '';

		if (hasPackage && !packageDescription) {
			packageDescription = defaultDescription;
		} else if (!hasPackage) {
			packageDescription = '';
		}

		// Dispatch change event to parent
		dispatch('change', {
			hasPackage,
			packageDescription
		});
	}

	function handleDescriptionInput() {
		validationError = '';

		// Dispatch change event to parent
		dispatch('change', {
			hasPackage,
			packageDescription
		});
	}

	// Validate package description
	export function validate(): boolean {
		if (hasPackage && (!packageDescription || packageDescription.trim() === '')) {
			validationError = 'Package description is required when package is checked';
			return false;
		}
		validationError = '';
		return true;
	}

	// Check if form is valid for button state
	export function isValid(): boolean {
		return !hasPackage || (packageDescription && packageDescription.trim() !== '');
	}
</script>

<div class="border-t border-gray-200 pt-2">
	<Label class="mb-1 text-base font-medium text-gray-900">Package Information</Label>

	<div class="space-y-1">
		<div class="flex items-center">
			<Checkbox bind:checked={hasPackage} on:change={handlePackageChange} class="mr-2" />
			<Label class="text-sm font-medium text-gray-700">{checkboxLabel}</Label>
		</div>

		{#if hasPackage}
			<div>
				<Label for="package-description" class="mb-1 block text-sm font-medium text-gray-700">
					Package Description <span class="text-red-500">*</span>
				</Label>
				<Textarea
					id="package-description"
					bind:value={packageDescription}
					placeholder="Describe the package contents and any special handling requirements"
					rows="2"
					class="w-full text-sm {validationError ? 'border-red-500' : ''}"
					on:input={handleDescriptionInput}
				/>
				{#if validationError}
					<p class="mt-1 text-sm text-red-600">{validationError}</p>
				{/if}
			</div>
		{/if}
	</div>
</div>
