<script lang="ts">
	import { onMount } from 'svelte';
	import { Badge } from 'flowbite-svelte';
	import { ChevronDownOutline } from 'flowbite-svelte-icons';
	import {
		updateRideStatus,
		getStatusColor,
		type RideStatus
	} from '$lib/services/ride-status.service';
	import { updateRideCancelReason, type CancelReason } from '$lib/services/cancel-reasons.service';
	import { updateRidePackageInfo, updateRideWithMultipleFields } from '$lib/services/rides.service';
	import CancelReasonModal from './CancelReasonModal.svelte';
	import RideCompletionModal from './RideCompletionModal.svelte';

	export let rideId: number;
	export let currentStatusId: number;
	export let rideStatuses: RideStatus[] = []; // Pre-loaded statuses
	export let cancelReasons: CancelReason[] = []; // Pre-loaded cancel reasons
	export let onStatusChange: (newStatusId: number) => void = () => {};
	export let onCancelReasonChange: (cancelReasonId: number | null) => void = () => {};
	export let onPackageChange: (hasPackage: boolean, packageDescription: string) => void = () => {};

	let currentStatus: RideStatus | null = null;
	let updating = false;
	let error = '';
	let dropdownOpen = false;
	let dropdownElement: HTMLDivElement;
	let buttonElement: HTMLButtonElement;
	let showCancelReasonModal = false;
	let showCompletionModal = false;
	let pendingStatusChange: RideStatus | null = null;
	let modalError = ''; // Error message to show in modals
	let displayText = ''; // Reactive display text
	let badgeColor = 'gray'; // Reactive badge color

	// Reactive statement to sync currentStatus with currentStatusId prop
	$: {
		if (rideStatuses.length > 0 && currentStatusId) {
			const foundStatus = rideStatuses.find((status) => status.id === currentStatusId);
			if (foundStatus && (!currentStatus || currentStatus.id !== foundStatus.id)) {
				currentStatus = { ...foundStatus };
			}
		}
	}

	// Reactive statement to update display text and badge color
	$: {
		if (updating) {
			displayText = 'Updating...';
			badgeColor = 'gray';
		} else if (error) {
			displayText = 'Error';
			badgeColor = 'red';
		} else if (currentStatus && currentStatus.name) {
			displayText = currentStatus.name;
			badgeColor = getStatusColor(currentStatus);
		} else if (rideStatuses.length === 0) {
			displayText = 'Loading...';
			badgeColor = 'gray';
		} else if (currentStatusId && rideStatuses.length > 0) {
			// Fallback: find status by ID if currentStatus is not set
			const fallbackStatus = rideStatuses.find((s) => s.id === currentStatusId);
			if (fallbackStatus) {
				displayText = fallbackStatus.name;
				badgeColor = getStatusColor(fallbackStatus);
			} else {
				displayText = `Status ${currentStatusId}`;
				badgeColor = 'gray';
			}
		} else {
			displayText = `Status ${currentStatusId}`;
			badgeColor = 'gray';
		}
	}

	// Close dropdown when updating starts to prevent race conditions
	$: if (updating) {
		dropdownOpen = false;
	}

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownElement &&
				!dropdownElement.contains(event.target as Node) &&
				buttonElement &&
				!buttonElement.contains(event.target as Node)
			) {
				dropdownOpen = false;
			}
		};

		document.addEventListener('click', handleClickOutside);

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	async function handleStatusChange(newStatus: RideStatus) {
		// Prevent race conditions by checking if already updating or same status
		if (newStatus.id === currentStatusId || updating) return;

		// Close dropdown immediately to prevent further interactions
		dropdownOpen = false;

		// Check if the new status is "Cancelled" - show cancel reason modal
		const isCancelledStatus =
			newStatus.name.toLowerCase().includes('cancelled') ||
			newStatus.name.toLowerCase().includes('canceled');

		if (isCancelledStatus && cancelReasons.length > 0) {
			pendingStatusChange = newStatus;
			modalError = '';
			updating = true;
			showCancelReasonModal = true;
			return;
		}

		// Check if the new status is "Completed" - show completion modal
		const isCompletedStatus =
			newStatus.name.toLowerCase().includes('completed') ||
			newStatus.name.toLowerCase().includes('finished') ||
			newStatus.name.toLowerCase().includes('done');

		if (isCompletedStatus) {
			pendingStatusChange = newStatus;
			modalError = '';
			updating = true;
			showCompletionModal = true;
			return;
		}

		// For other statuses, proceed directly
		await updateStatusDirectly(newStatus);
	}

	async function updateStatusDirectly(newStatus: RideStatus) {
		if (updating) return;

		try {
			updating = true;
			error = '';

			// Check if we're changing from a cancelled or completed status to a pending status
			const currentIsCancelled =
				currentStatus &&
				(currentStatus.name.toLowerCase().includes('cancelled') ||
					currentStatus.name.toLowerCase().includes('canceled'));
			const currentIsCompleted =
				currentStatus &&
				(currentStatus.name.toLowerCase().includes('completed') ||
					currentStatus.name.toLowerCase().includes('finished') ||
					currentStatus.name.toLowerCase().includes('done'));
			const newIsPending = newStatus.name.toLowerCase().includes('pending');

			// If changing from cancelled or completed to pending, reset cancel reason and package info
			if ((currentIsCancelled || currentIsCompleted) && newIsPending) {
				const updateData = {
					ride_status_id: newStatus.id,
					cancel_reason_id: null,
					has_package: false,
					package_description: ''
				};

				const success = await updateRideWithMultipleFields(rideId, updateData);

				if (success) {
					// Update local state immediately for instant UI feedback
					currentStatus = { ...newStatus };
					displayText = newStatus.name;
					badgeColor = getStatusColor(newStatus);

					// Update parent component state
					onStatusChange(newStatus.id);
					onCancelReasonChange(null);
					onPackageChange(false, '');
				} else {
					error = 'Failed to update status and reset fields';
				}
			} else {
				// For other status changes, just update the status
				const success = await updateRideStatus(rideId, newStatus.id);

				if (success) {
					// Update local state immediately for instant UI feedback
					currentStatus = { ...newStatus };
					displayText = newStatus.name;
					badgeColor = getStatusColor(newStatus);

					// Update parent component state
					onStatusChange(newStatus.id);
				} else {
					error = 'Failed to update status';
				}
			}
		} catch (err) {
			error = 'Failed to update status';
		} finally {
			updating = false;
		}
	}

	function toggleDropdown() {
		if (updating || rideStatuses.length === 0) return;
		dropdownOpen = !dropdownOpen;
	}

	async function handleCancelReasonConfirm(event: CustomEvent) {
		const { cancelReasonId, hasPackage, packageDescription } = event.detail;

		if (!pendingStatusChange) return;

		if (updating) {
			updating = false;
		}

		try {
			updating = true;
			error = '';
			modalError = '';

			// Update all fields in a single PATCH request to avoid race conditions
			const success = await updateRideWithMultipleFields(rideId, {
				ride_status_id: pendingStatusChange.id,
				cancel_reason_id: cancelReasonId,
				has_package: hasPackage,
				package_description: hasPackage ? packageDescription : ''
			});

			if (success) {
				// Update local state immediately for instant UI feedback
				currentStatus = { ...pendingStatusChange };
				displayText = pendingStatusChange.name;
				badgeColor = getStatusColor(pendingStatusChange);

				// Update parent component state
				onStatusChange(pendingStatusChange.id);
				onCancelReasonChange(cancelReasonId);
				onPackageChange(hasPackage, packageDescription);
				showCancelReasonModal = false;
				pendingStatusChange = null;
			} else {
				modalError =
					'Failed to update status, cancel reason, or package information. Please try again.';
			}
		} catch (err) {
			modalError = 'An error occurred while updating. Please try again.';
		} finally {
			updating = false;
		}
	}

	async function handleCompletionConfirm(event: CustomEvent) {
		const { hasPackage, packageDescription } = event.detail;

		if (!pendingStatusChange) return;

		if (updating) {
			updating = false;
		}

		try {
			updating = true;
			error = '';
			modalError = '';

			const currentIsCancelled =
				currentStatus &&
				(currentStatus.name.toLowerCase().includes('cancelled') ||
					currentStatus.name.toLowerCase().includes('canceled'));

			const updateData: any = {
				ride_status_id: pendingStatusChange.id,
				has_package: hasPackage,
				package_description: hasPackage ? packageDescription : ''
			};

			if (currentIsCancelled) {
				updateData.cancel_reason_id = null;
			}

			const success = await updateRideWithMultipleFields(rideId, updateData);

			if (success) {
				// Update local state immediately for instant UI feedback
				currentStatus = { ...pendingStatusChange };
				displayText = pendingStatusChange.name;
				badgeColor = getStatusColor(pendingStatusChange);

				// Update parent component state
				onStatusChange(pendingStatusChange.id);
				onPackageChange(hasPackage, packageDescription);
				if (currentIsCancelled) {
					onCancelReasonChange(null);
				}
				showCompletionModal = false;
				pendingStatusChange = null;
			} else {
				modalError =
					'Failed to update status, package information, or cancel reason. Please try again.';
			}
		} catch (err) {
			modalError = 'An error occurred while updating. Please try again.';
		} finally {
			updating = false;
		}
	}

	function handleCancelReasonCancel() {
		showCancelReasonModal = false;
		pendingStatusChange = null;
		modalError = '';
		updating = false;
	}

	function handleCancelReasonClose() {
		showCancelReasonModal = false;
		pendingStatusChange = null;
		modalError = '';
		updating = false;
	}

	function handleCompletionClose() {
		showCompletionModal = false;
		pendingStatusChange = null;
		modalError = '';
		updating = false;
	}

	function handleCompletionCancel() {
		showCompletionModal = false;
		pendingStatusChange = null;
		modalError = '';
		updating = false;
	}
</script>

<div class="status-dropdown-container">
	{#if error && !updating}
		<div class="error-state" title={error}>
			<Badge color="red" class="status-badge cursor-pointer">Error</Badge>
		</div>
	{:else}
		<button
			bind:this={buttonElement}
			class="status-button"
			class:updating
			class:disabled={updating || rideStatuses.length === 0}
			disabled={updating || rideStatuses.length === 0}
			on:click={toggleDropdown}
			title={updating ? 'Updating status...' : 'Click to change status'}
		>
			<Badge color={badgeColor} class="status-badge flex cursor-pointer items-center gap-1">
				{#if updating}
					<div class="spinner"></div>
				{/if}
				<span>{displayText}</span>
				{#if !updating && rideStatuses.length > 0}
					<ChevronDownOutline class="h-3 w-3" />
				{/if}
			</Badge>
		</button>

		{#if dropdownOpen}
			<div bind:this={dropdownElement} class="dropdown-menu">
				{#if rideStatuses.length === 0}
					<div class="dropdown-item disabled">No statuses available</div>
				{:else}
					{#each rideStatuses as status (status.id)}
						<button
							class="dropdown-item {status.id === currentStatusId ? 'current' : ''} {updating
								? 'disabled'
								: ''}"
							on:click={() => handleStatusChange(status)}
							disabled={status.id === currentStatusId || updating}
						>
							<Badge color={getStatusColor(status)} size="sm">
								{status.name}
							</Badge>
							{#if status.id === currentStatusId}
								<span class="current-label">Current</span>
							{/if}
						</button>
					{/each}
				{/if}
			</div>
		{/if}
	{/if}
</div>

<!-- Cancel Reason Modal -->
{#if showCancelReasonModal}
	<CancelReasonModal
		{rideId}
		{cancelReasons}
		error={modalError}
		on:confirm={handleCancelReasonConfirm}
		on:cancel={handleCancelReasonCancel}
		on:close={handleCancelReasonClose}
	/>
{/if}

<!-- Ride Completion Modal -->
{#if showCompletionModal}
	<RideCompletionModal
		{rideId}
		error={modalError}
		on:confirm={handleCompletionConfirm}
		on:cancel={handleCompletionCancel}
		on:close={handleCompletionClose}
	/>
{/if}

<style>
	.status-dropdown-container {
		position: relative;
		display: inline-block;
		width: 140px; /* Static width to match tracking buttons */
	}

	.status-button {
		background: none;
		border: none;
		padding: 0;
		cursor: pointer;
		transition: opacity 0.2s ease;
		width: 100%;
		min-height: 36px; /* Match tracking button height */
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.status-button:disabled {
		cursor: not-allowed;
		opacity: 0.6;
	}

	.status-button.updating {
		cursor: wait;
	}

	:global(.status-badge) {
		width: 100% !important;
		min-height: 32px !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		padding: 6px 12px !important;
		font-size: 13px !important;
		font-weight: 600 !important;
	}

	.error-state {
		cursor: help;
	}

	.dropdown-menu {
		position: absolute;
		top: 100%;
		left: 0;
		z-index: 50;
		min-width: 200px;
		margin-top: 4px;
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
		max-height: 300px;
		overflow-y: auto;
	}

	.dropdown-item {
		display: flex;
		align-items: center;
		gap: 8px;
		width: 100%;
		padding: 8px 12px;
		text-align: left;
		background: none;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
		font-size: 14px;
	}

	.dropdown-item:hover:not(.disabled):not(.current) {
		background-color: #f9fafb;
	}

	.dropdown-item.current {
		background-color: #f3f4f6;
		font-weight: 500;
		cursor: default;
	}

	.dropdown-item.disabled {
		color: #9ca3af;
		cursor: not-allowed;
	}

	.dropdown-item:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.current-label {
		font-size: 12px;
		color: #6b7280;
		margin-left: auto;
	}

	.spinner {
		width: 12px;
		height: 12px;
		border: 2px solid rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		border-top-color: white;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
