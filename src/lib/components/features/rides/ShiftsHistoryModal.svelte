<script lang="ts">
	import {
		Modal,
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Spinner,
		Badge
	} from 'flowbite-svelte';
	import {
		getShifts,
		enrichShiftsWithRouteInfo,
		type Shift
	} from '$lib/services/time-tracking.service';
	import { formatDateTime } from '$lib/utils/datetime';

	export let showModal: boolean;
	export let pilotEmail: string;
	export let onClose: () => void;

	let shifts: Shift[] = [];
	let loading = false;
	let error: string | null = null;

	$: if (showModal && pilotEmail) {
		loadShifts();
	}

	async function loadShifts() {
		try {
			loading = true;
			error = null;

			const rawShifts = await getShifts({
				pilot_email: pilotEmail,
				limit: 50,
				skip: 0
			});

			if (!rawShifts || rawShifts.length === 0) {
				shifts = [];
				return;
			}

			shifts = await enrichShiftsWithRouteInfo(rawShifts);
		} catch (err) {
			console.error('Error loading shifts:', err);
			error = err instanceof Error ? err.message : 'Failed to load shift history';
			shifts = [];
		} finally {
			loading = false;
		}
	}

	function calculateDuration(startTime: string, stopTime: string | null): string {
		if (!startTime || !stopTime) return '-';

		try {
			const start = new Date(startTime);
			const stop = new Date(stopTime);
			const diffMs = stop.getTime() - start.getTime();

			if (diffMs < 0) return '-';

			const hours = Math.floor(diffMs / (1000 * 60 * 60));
			const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

			return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		} catch {
			return '-';
		}
	}

	function getShiftStatus(shift: Shift): { text: string; color: string } {
		if (!shift.stop_time) {
			return { text: 'Active', color: 'green' };
		} else {
			return { text: 'Completed', color: 'blue' };
		}
	}

	function handleClose() {
		shifts = [];
		error = null;
		onClose();
	}
</script>

<Modal bind:open={showModal} size="xl" autoclose={false} on:close={handleClose}>
	<div class="p-6">
		<h3 class="mb-4 text-xl font-medium text-gray-900">Time Tracking History</h3>

		{#if pilotEmail}
			<p class="mb-4 text-sm text-gray-600">
				Showing shift history for operator: <span class="font-medium">{pilotEmail}</span>
			</p>
		{/if}

		{#if error}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{error}
			</div>
		{/if}

		{#if loading}
			<div class="flex flex-col items-center justify-center py-12">
				<Spinner size="8" class="mb-4" />
				<p class="text-gray-500">Loading shift history...</p>
			</div>
		{:else if shifts.length === 0 && !error}
			<div class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700">
				<p>No shift history found for this pilot.</p>
			</div>
		{:else}
			<div class="max-h-96 overflow-x-auto">
				<Table striped={true} hoverable={true}>
					<TableHead>
						<TableHeadCell>ID</TableHeadCell>
						<TableHeadCell>Route</TableHeadCell>
						<TableHeadCell>Ride ID</TableHeadCell>
						<TableHeadCell>Start Time</TableHeadCell>
						<TableHeadCell>Stop Time</TableHeadCell>
						<TableHeadCell>Duration</TableHeadCell>
						<TableHeadCell>Status</TableHeadCell>
						<TableHeadCell>Description</TableHeadCell>
					</TableHead>
					<TableBody class="divide-y">
						{#each shifts as shift (shift.id || shift.shift_id)}
							{@const status = getShiftStatus(shift)}
							<TableBodyRow>
								<TableBodyCell class="font-medium">
									{shift.id || shift.shift_id || '-'}
								</TableBodyCell>
								<TableBodyCell>
									{#if shift.start_location_name && shift.end_location_name}
										<div class="text-sm">
											<div class="font-medium">{shift.start_location_name}</div>
											<div class="text-gray-500">→ {shift.end_location_name}</div>
										</div>
									{:else}
										<span class="text-gray-500">Route #{shift.route_id}</span>
									{/if}
								</TableBodyCell>
								<TableBodyCell>
									{shift.ride_id || '-'}
								</TableBodyCell>
								<TableBodyCell class="text-sm">
									{formatDateTime(shift.start_time)}
								</TableBodyCell>
								<TableBodyCell class="text-sm">
									{shift.stop_time ? formatDateTime(shift.stop_time) : '-'}
								</TableBodyCell>
								<TableBodyCell class="text-sm font-medium">
									{calculateDuration(shift.start_time, shift.stop_time)}
								</TableBodyCell>
								<TableBodyCell>
									<Badge color={status.color} class="text-xs">
										{status.text}
									</Badge>
								</TableBodyCell>
								<TableBodyCell class="max-w-xs truncate text-sm">
									{shift.description || '-'}
								</TableBodyCell>
							</TableBodyRow>
						{/each}
					</TableBody>
				</Table>
			</div>

			{#if shifts.length >= 50}
				<p class="mt-2 text-xs text-gray-500">
					Showing latest 50 shifts. For complete history, visit the Shifts page.
				</p>
			{/if}
		{/if}

		<div class="mt-4 flex justify-end border-t pt-4">
			<button
				class="rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
				on:click={handleClose}
			>
				Close
			</button>
		</div>
	</div>
</Modal>

<style>
	:global(.modal-content) {
		max-height: 80vh;
		overflow-y: auto;
	}
</style>
