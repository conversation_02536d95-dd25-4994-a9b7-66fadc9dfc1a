<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Modal, Button, Label } from 'flowbite-svelte';
	import type { CancelReason } from '$lib/services/cancel-reasons.service';
	import PackageInfoSection from './PackageInfoSection.svelte';

	export let rideId: number;
	export let cancelReasons: CancelReason[] = [];
	export let error = ''; // Error message from parent

	let selectedCancelReasonId: number | null = null;
	let isUpdating = false;
	let open = true;

	// Package delivery state
	let hasPackage = false;
	let packageDescription = '';
	let packageInfoSection: PackageInfoSection;

	const dispatch = createEventDispatcher();

	function handleClose() {
		dispatch('close');
	}

	function handleCancel() {
		dispatch('cancel');
	}

	async function handleConfirm() {
		if (selectedCancelReasonId === null) {
			return;
		}

		// Validate package information using the shared component
		if (!packageInfoSection.validate()) {
			return;
		}

		try {
			isUpdating = true;
			dispatch('confirm', {
				cancelReasonId: selectedCancelReasonId,
				hasPackage,
				packageDescription: hasPackage ? packageDescription : ''
			});
		} catch (error) {
			console.error('Error confirming cancel reason:', error);
		} finally {
			isUpdating = false;
		}
	}

	function selectReason(reasonId: number) {
		selectedCancelReasonId = reasonId;
	}

	function handlePackageChange(event: CustomEvent) {
		hasPackage = event.detail.hasPackage;
		packageDescription = event.detail.packageDescription;
	}
</script>

<Modal bind:open size="lg" autoclose={false} dismissable={false} class="mx-auto w-full max-w-lg">
	<div class="flex items-center justify-between rounded-t border-b px-4 py-3 md:px-6">
		<h3 class="text-lg font-semibold text-gray-900">
			Select Cancellation Reason for Ride #{rideId}
		</h3>
		<button
			type="button"
			class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900"
			on:click={handleClose}
			aria-label="Close modal"
		>
			<svg
				class="h-3 w-3"
				aria-hidden="true"
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 14 14"
			>
				<path
					stroke="currentColor"
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
				/>
			</svg>
		</button>
	</div>

	<div class="px-4 py-3 md:px-6">
		<!-- Error Display -->
		{#if error}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{error}
			</div>
		{/if}

		<div class="mb-2">
			<Label class="mb-1 text-base font-medium text-gray-900">
				Please select a reason for cancelling this ride:
			</Label>
		</div>

		{#if cancelReasons.length === 0}
			<div class="text-center text-gray-500">No cancel reasons available</div>
		{:else}
			<div class="mb-3 space-y-1" role="radiogroup" aria-labelledby="cancel-reasons-label">
				<div id="cancel-reasons-label" class="sr-only">Select a cancellation reason</div>
				{#each cancelReasons as reason (reason.id)}
					<div
						class="w-full cursor-pointer rounded border px-3 py-1.5 transition-colors hover:bg-gray-50 {selectedCancelReasonId ===
						reason.id
							? 'border-blue-500 bg-blue-50'
							: 'border-gray-200'}"
						role="radio"
						aria-checked={selectedCancelReasonId === reason.id}
						aria-labelledby="reason-{reason.id}-label"
						tabindex="0"
						on:click={() => selectReason(reason.id)}
						on:keydown={(e) => e.key === 'Enter' && selectReason(reason.id)}
					>
						<div class="flex items-center gap-2">
							<div
								class="h-3 w-3 flex-shrink-0 rounded-full border-2 {selectedCancelReasonId ===
								reason.id
									? 'border-blue-500 bg-blue-500'
									: 'border-gray-300'}"
								aria-hidden="true"
							>
								{#if selectedCancelReasonId === reason.id}
									<div
										class="h-full w-full rounded-full bg-white"
										style="transform: scale(0.4);"
									></div>
								{/if}
							</div>
							<div class="min-w-0 flex-1">
								<div
									id="reason-{reason.id}-label"
									class="break-words text-sm font-medium text-gray-900"
								>
									{reason.name}
								</div>
								{#if reason.description && reason.description !== reason.name}
									<div class="break-words text-xs text-gray-600">{reason.description}</div>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}

		<!-- Package Information Section -->
		<PackageInfoSection
			bind:this={packageInfoSection}
			bind:hasPackage
			bind:packageDescription
			checkboxLabel="Samples delivered by Car"
			defaultDescription="Delivered by car with X samples"
			on:change={handlePackageChange}
		/>
	</div>

	<div
		class="flex items-center justify-end space-x-2 rounded-b border-t border-gray-200 px-4 py-2 md:px-6"
	>
		<Button color="alternative" size="sm" on:click={handleCancel} disabled={isUpdating}>
			Cancel
		</Button>
		<Button
			color="primary"
			size="sm"
			on:click={handleConfirm}
			disabled={selectedCancelReasonId === null || isUpdating || !packageInfoSection?.isValid()}
		>
			{#if isUpdating}
				Updating...
			{:else}
				Confirm Cancellation
			{/if}
		</Button>
	</div>
</Modal>
