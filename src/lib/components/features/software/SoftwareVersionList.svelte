<script lang="ts">
	import { useQuery } from '@sveltestack/svelte-query';
	import { Tabs, TabItem, Spinner } from 'flowbite-svelte';
	import VersionTable from './VersionTable.svelte';
	import { fetchSoftwareVersions, fetchSoftwareByType } from '$services/software.service';
	import { formatDateTimeDetailed } from '$lib/utils/datetime';

	export let softwareType: string = '';

	let activeTab = 0;
	let sortBy = 'createdAt';
	let sortDirection: 'asc' | 'desc' = 'desc';

	const queryFn = softwareType
		? () => fetchSoftwareByType(softwareType)
		: () => fetchSoftwareVersions();

	const softwareVersionsQuery = useQuery(['software-versions', softwareType], queryFn, {
		refetchInterval: 30000,
		onSuccess: (data) => {
			console.log(data);
		},
		onError: (error) => {
			console.error('API error:', error);
		}
	});

	function toggleSort(field: 'createdAt' | 'updatedAt'): void {
		if (sortBy === field) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortBy = field;
			sortDirection = 'desc';
		}
	}

	function handleTabChange(event: CustomEvent) {
		activeTab = event.detail.index;
		console.log('Active tab index:', activeTab);
	}

	$: uniqueTypes = $softwareVersionsQuery.data?.length
		? Array.from(new Set($softwareVersionsQuery.data.map((item) => item.softwareVersionType.name)))
		: [];

	$: currentTypeData = $softwareVersionsQuery.data
		? $softwareVersionsQuery.data.filter(
				(item) => item.softwareVersionType.name === uniqueTypes[activeTab]
			)
		: [];

	$: console.log('Query status:', {
		loading: $softwareVersionsQuery.isLoading,
		error: $softwareVersionsQuery.error,
		data: $softwareVersionsQuery.data
	});
</script>

<div class="flex flex-col">
	<div class="rounded-lg bg-white p-4 shadow">
		{#if $softwareVersionsQuery.isLoading}
			<div class="flex flex-col items-center justify-center py-12">
				<Spinner size="8" class="common-spinner mb-4" />
				<p class="text-gray-500">Loading software versions...</p>
			</div>
		{:else if $softwareVersionsQuery.isError}
			<div
				class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
				role="alert"
			>
				<p>Error loading data: {$softwareVersionsQuery.error?.message}</p>
				<button
					class="mt-2 text-blue-500 hover:underline"
					on:click={() => softwareVersionsQuery.refetch()}>Retry</button
				>
			</div>
		{:else if uniqueTypes.length === 0}
			<div
				class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700"
				role="alert"
			>
				<p>No software versions available</p>
			</div>
		{:else}
			<Tabs on:tabChange={handleTabChange} class="custom-tabs" tabStyle="pill">
				{#each uniqueTypes as type, index}
					<TabItem
						activeClasses="py-3 px-4 text-gray-800 bg-primary-100 rounded-lg"
						title={type}
						open={index === activeTab}
					>
						<div class="overflow-x-auto pt-4">
							<VersionTable
								data={$softwareVersionsQuery.data.filter(
									(item) => item.softwareVersionType.name === type
								)}
								{sortBy}
								{sortDirection}
								{toggleSort}
								formatDate={formatDateTimeDetailed}
							/>
						</div>
					</TabItem>
				{/each}
			</Tabs>
		{/if}
	</div>
</div>

<style>
	:global(.custom-tabs .tab-underline) {
		@apply border-b-2 border-transparent text-gray-600 transition-colors hover:text-gray-800;
	}
	:global(.custom-tabs .tab-underline.active) {
		@apply border-[#48a851] text-[#48a851];
	}
</style>
