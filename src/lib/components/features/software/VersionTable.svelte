<script lang="ts">
	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Toast
	} from 'flowbite-svelte';
	import { CheckCircleSolid } from 'flowbite-svelte-icons';
	import ClipboardDocumentIcon from 'flowbite-svelte-icons/FileCopyOutline.svelte';
	import type { Software } from '$types/software.types';

	interface ToastItem {
		id: number;
		message: string;
	}

	export let data: Software[];
	export let sortBy: string;
	export let sortDirection: 'asc' | 'desc';
	export let toggleSort: (field: 'createdAt' | 'updatedAt') => void;
	export let formatDate: (date: string) => string;

	$: sortedData = [...data].sort((a, b) => {
		const modifier = sortDirection === 'asc' ? 1 : -1;
		return modifier * (new Date(a[sortBy]).getTime() - new Date(b[sortBy]).getTime());
	});

	let toasts: ToastItem[] = [];

	function showToast(message: string) {
		const id = Date.now();
		toasts = [...toasts, { id, message }];
		setTimeout(() => {
			toasts = toasts.filter((t) => t.id !== id);
		}, 3000);
	}

	function copyText(text: string): void {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				showToast('Copied to clipboard');
			})
			.catch((err) => {
				console.error('Copy failed', err);
			});
	}
</script>

<Table striped={true} hoverable={true} class="rounded-lg bg-white shadow">
	<TableHead>
		<TableHeadCell>ID</TableHeadCell>
		<TableHeadCell>Version</TableHeadCell>
		<TableHeadCell on:click={() => toggleSort('createdAt')} class="cursor-pointer">
			<div class="flex items-center">
				<span>Created At</span>
				{#if sortBy === 'createdAt'}
					<span class="ml-1 text-primary-500">{sortDirection === 'asc' ? '↑' : '↓'}</span>
				{/if}
			</div>
		</TableHeadCell>
		<TableHeadCell on:click={() => toggleSort('updatedAt')} class="cursor-pointer">
			<div class="flex items-center">
				<span>Updated At</span>
				{#if sortBy === 'updatedAt'}
					<span class="ml-1 text-primary-500">{sortDirection === 'asc' ? '↑' : '↓'}</span>
				{/if}
			</div>
		</TableHeadCell>
	</TableHead>

	<TableBody class="divide-y">
		{#each sortedData as version}
			<TableBodyRow>
				<TableBodyCell>{version.id}</TableBodyCell>
				<TableBodyCell class="flex items-center">
					<span class="font-medium text-primary-700">{version.name}</span>
					<button
						on:click={() => copyText(version.name)}
						title="Copy version"
						class="ml-2 focus:outline-none"
						type="button"
					>
						<ClipboardDocumentIcon class="h-4 w-4 text-gray-500 hover:text-gray-700" />
					</button>
				</TableBodyCell>
				<TableBodyCell>{formatDate(version.createdAt)}</TableBodyCell>
				<TableBodyCell>{formatDate(version.updatedAt)}</TableBodyCell>
			</TableBodyRow>
		{/each}
	</TableBody>
</Table>

<div
	style="position: fixed; top: 1rem; right: 1rem; display: flex; flex-direction: column; gap: 0.5rem; z-index: 50;"
>
	{#each toasts as toast (toast.id)}
		<Toast class="mb-2">
			<svelte:fragment slot="icon">
				<CheckCircleSolid class="h-5 w-5" />
				<span class="sr-only">Check icon</span>
			</svelte:fragment>
			{toast.message}
		</Toast>
	{/each}
</div>
