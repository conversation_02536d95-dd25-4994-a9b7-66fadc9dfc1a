<script lang="ts">
	import { onMount } from 'svelte';
	import {
		getShifts,
		getUniquePilots,
		enrichShiftsWithRouteInfo,
		type Shift
	} from '$lib/services/time-tracking.service';
	import { fetchCustomers, type Customer } from '$lib/services/customers.service';
	import { fetchRoutes, type Route } from '$lib/services/routes.service';
	import { exportShiftsCSV } from '$lib/services/export.service';
	import {
		Button,
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Spinner,
		Datepicker,
		Label,
		Select
	} from 'flowbite-svelte';
	import { FilterSolid, FileSolid } from 'flowbite-svelte-icons';
	import { formatDateTime } from '$lib/utils/datetime';
	import '$lib/styles/variables.css';
	import './shiftsList.styles.css';

	let shifts: Shift[] = [];
	let loading = true;
	let error: string | null = null;

	let csvExporting = false;

	let customers: Customer[] = [];
	let selectedCustomerId: number | null = null;

	let startDate: string | null = null;
	let endDate: string | null = null;
	let selectedPilots: string[] = [];
	let selectedRouteId: number | null = null;
	let routes: Route[] = [];
	let pilotsOptions: string[] = [];
	let pilotEmail: string = '';

	const formatDateForApi = (date: string | Date) => {
		if (!date) return null;

		try {
			if (date instanceof Date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			} else {
				if (date.includes('T') || date.includes(' ')) {
					const dateObj = new Date(date);
					return dateObj.toISOString().split('T')[0];
				} else {
					return date;
				}
			}
		} catch (_) {
			return null;
		}
	};

	const loadPilots = async () => {
		try {
			pilotsOptions = await getUniquePilots();
		} catch (err) {
			console.error('Error loading pilots:', err);
		}
	};

	const loadRoutes = async () => {
		try {
			routes = await fetchRoutes(0, 1000);
		} catch (err) {
			console.error('Error loading routes:', err);
		}
	};

	const loadCustomers = async () => {
		try {
			customers = await fetchCustomers(0, 1000);
		} catch (err) {
			console.error('Error loading customers:', err);
		}
	};

	const loadShifts = async () => {
		try {
			loading = true;

			if (selectedPilots.length > 1) {
				let allShifts = [];
				for (const pilotEmail of selectedPilots) {
					const params = {
						skip: 0,
						limit: 100,
						pilot_email: pilotEmail
					};

					const formattedStartDate = startDate ? formatDateForApi(startDate) : null;
					const formattedEndDate = endDate ? formatDateForApi(endDate) : null;

					if (formattedStartDate) {
						params.start_date = formattedStartDate;
					}

					if (formattedEndDate) {
						params.end_date = formattedEndDate;
					}

					if (selectedCustomerId) {
						params.customer_id = selectedCustomerId;
					}

					if (selectedRouteId) {
						params.route_id = selectedRouteId;
					}

					const pilotShifts = await getShifts(params);
					allShifts = [...allShifts, ...pilotShifts];
				}
				allShifts = await enrichShiftsWithRouteInfo(allShifts);
				shifts = allShifts;
			} else {
				const params = {
					skip: 0,
					limit: 100
				};

				const formattedStartDate = startDate ? formatDateForApi(startDate) : null;
				const formattedEndDate = endDate ? formatDateForApi(endDate) : null;

				if (formattedStartDate) {
					params.start_date = formattedStartDate;
				}

				if (formattedEndDate) {
					params.end_date = formattedEndDate;
				}

				if (selectedCustomerId) {
					params.customer_id = selectedCustomerId;
				}

				if (selectedRouteId) {
					params.route_id = selectedRouteId;
				}

				if (selectedPilots.length === 1) {
					params.pilot_email = selectedPilots[0];
				}

				let allShifts = await getShifts(params);
				allShifts = await enrichShiftsWithRouteInfo(allShifts);
				shifts = allShifts;
			}
		} catch (err) {
			console.error('Error loading shifts:', err);
			error = err instanceof Error ? err.message : 'Unknown error loading shifts';
		} finally {
			loading = false;
		}
	};

	const addPilot = async () => {
		if (pilotEmail && !selectedPilots.includes(pilotEmail)) {
			selectedPilots = [...selectedPilots, pilotEmail];
			pilotEmail = '';
			await applyFilters();
		}
	};

	const removePilot = async (email: string) => {
		selectedPilots = selectedPilots.filter((p) => p !== email);
		await applyFilters();
	};

	const resetAllFilters = () => {
		startDate = null;
		endDate = null;
		selectedPilots = [];
		pilotEmail = '';
		selectedRouteId = null;
		selectedCustomerId = null;
	};

	const applyFilters = async () => {
		await loadShifts();
	};

	const clearFilters = async () => {
		resetAllFilters();
		await loadShifts();
	};

	const clearPilots = async () => {
		selectedPilots = [];
		await applyFilters();
	};

	const hasActiveFilters = () => {
		return (
			startDate || endDate || selectedPilots.length > 0 || selectedRouteId || selectedCustomerId
		);
	};

	onMount(() => {
		loadPilots();
		loadRoutes();
		loadCustomers();
		loadShifts();
	});

	const exportCSV = async () => {
		try {
			csvExporting = true;

			const params = {
				start_date: startDate ? formatDateForApi(startDate) : undefined,
				end_date: endDate ? formatDateForApi(endDate) : undefined,
				route_id: selectedRouteId || undefined,
				customer_id: selectedCustomerId || undefined,
				pilot_email:
					selectedPilots.length === 1
						? [selectedPilots[0]]
						: selectedPilots.length > 1
							? selectedPilots
							: undefined
			};

			await exportShiftsCSV(params);
		} catch (error) {
			console.error('Error exporting CSV:', error);
		} finally {
			csvExporting = false;
		}
	};
</script>

<div class="flex min-h-screen flex-col py-8">
	<div class="w-full flex-1 px-6">
		<div class="mb-6 flex items-center justify-between">
			<div>
				<h2 class="text-2xl font-bold">Time Tracking History</h2>
				<p class="text-gray-600">View history of time tracked on routes</p>
			</div>
		</div>

		<div class="mb-6 rounded-lg bg-white p-6 shadow">
			<h3 class="mb-4 text-lg font-semibold">Filter Options</h3>

			<div class="flex flex-col gap-6 md:flex-row">
				<div class="w-full space-y-4 md:w-1/2">
					<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
						<div>
							<Label for="start-date" class="mb-2">Start Date</Label>
							<Datepicker
								id="start-date"
								bind:value={startDate}
								format="dd-MM-yyyy"
								on:change={applyFilters}
							/>
						</div>
						<div>
							<Label for="end-date" class="mb-2">End Date</Label>
							<Datepicker
								id="end-date"
								bind:value={endDate}
								format="dd-MM-yyyy"
								on:change={applyFilters}
							/>
						</div>
					</div>
					<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
						<div>
							<Label for="route" class="mb-2">Route</Label>
							<Select id="route" bind:value={selectedRouteId} on:change={applyFilters}>
								<option value={null}>All routes</option>
								{#each routes as route}
									<option value={route.id}>
										{route.external_route_id || `Route ${route.id}`}: {route.start_location_name} → {route.end_location_name}
									</option>
								{/each}
							</Select>
						</div>
						<div>
							<Label for="customer" class="mb-2">Customer</Label>
							<Select id="customer" bind:value={selectedCustomerId} on:change={applyFilters}>
								<option value={null}>All customers</option>
								{#each customers as customer}
									<option value={customer.id}>{customer.name}</option>
								{/each}
							</Select>
						</div>
					</div>
				</div>

				<div class="pilots-column w-full md:w-1/2">
					<div class="flex h-full flex-col">
						<div class="mb-2 flex items-center justify-between">
							<Label for="pilot-email" class="text-base font-medium">Pilot Emails</Label>
							{#if selectedPilots.length > 0}
								<button
									class="text-xs text-gray-500 underline hover:text-gray-700"
									on:click={clearPilots}
								>
									Clear all
								</button>
							{/if}
						</div>

						<div class="flex-1">
							<Select id="pilot-email" bind:value={pilotEmail} on:change={addPilot}>
								<option value="">Select pilot to add to filters</option>
								{#each pilotsOptions.filter((p) => !selectedPilots.includes(p)) as pilot}
									<option value={pilot}>{pilot}</option>
								{/each}
							</Select>

							{#if selectedPilots.length > 0}
								<div class="mt-3">
									<div class="flex flex-wrap gap-1">
										{#each selectedPilots as pilot}
											<span
												class="inline-flex items-center rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800 transition-all hover:bg-blue-200"
											>
												{pilot}
												<button
													type="button"
													class="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800 focus:bg-blue-500 focus:text-white focus:outline-none"
													on:click={() => removePilot(pilot)}
												>
													<span class="sr-only">Remove {pilot}</span>
													<svg class="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
														<path stroke-linecap="round" stroke-width="1.5" d="M1 1l6 6m0-6L1 7" />
													</svg>
												</button>
											</span>
										{/each}
									</div>
									<p class="mt-2 text-xs text-gray-500">
										{selectedPilots.length} pilot{selectedPilots.length !== 1 ? 's' : ''} selected
									</p>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>

			<div class="mt-6 flex justify-end gap-2">
				<Button
					color="alternative"
					on:click={clearFilters}
					disabled={!hasActiveFilters()}
					class="btn-secondary {!hasActiveFilters() ? 'cursor-not-allowed opacity-50' : ''}"
				>
					Clear Filters
				</Button>
				<Button
					class="btn-primary flex items-center gap-2 {hasActiveFilters() ? 'animate-pulse' : ''}"
					on:click={applyFilters}
				>
					<FilterSolid class="h-4 w-4" />
					Apply Filters {hasActiveFilters() && selectedPilots.length > 0
						? `(${selectedPilots.length} pilots)`
						: ''}
				</Button>
			</div>
		</div>

		<div class="rounded-lg bg-white p-4 shadow">
			{#if loading}
				<div class="flex flex-col items-center justify-center py-12">
					<Spinner size="8" class="common-spinner mb-4" />
					<p class="text-gray-500">Loading shifts data...</p>
				</div>
			{:else if error}
				<div
					class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
					role="alert"
				>
					<p>{error}</p>
				</div>
			{:else if shifts.length === 0}
				<div
					class="mb-4 rounded border border-yellow-400 bg-yellow-100 px-4 py-3 text-yellow-700"
					role="alert"
				>
					<p>No shifts available</p>
				</div>
			{:else}
				<div class="mb-4 flex items-center justify-end">
					<Button
						size="md"
						color="alternative"
						class="flex items-center gap-2"
						disabled={csvExporting}
						on:click={exportCSV}
					>
						{#if csvExporting}
							<div
								class="mr-1 h-4 w-4 animate-spin rounded-full border-2 border-gray-600 border-t-transparent"
							></div>
							<span>Exporting...</span>
						{:else}
							<FileSolid class="mr-1 h-4 w-4" />
							<span>Export CSV</span>
						{/if}
					</Button>
				</div>

				<div class="overflow-x-auto">
					<Table striped={true} hoverable={true} class="rounded-lg bg-white shadow">
						<TableHead class="bg-gray-100">
							<TableHeadCell class="font-medium text-gray-700">Pilot Email</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Route</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Start Time</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">End Time</TableHeadCell>
							<TableHeadCell class="font-medium text-gray-700">Duration</TableHeadCell>
						</TableHead>
						<TableBody class="divide-y">
							{#each shifts as shift (shift.pilot_email + shift.route_id + shift.start_time)}
								<TableBodyRow>
									<TableBodyCell>{shift.pilot_email}</TableBodyCell>
									<TableBodyCell>
										{#if shift.start_location_name && shift.end_location_name}
											{shift.start_location_name} → {shift.end_location_name}
										{:else}
											Route ID: {shift.route_id}
										{/if}
									</TableBodyCell>
									<TableBodyCell>{formatDateTime(shift.start_time)}</TableBodyCell>
									<TableBodyCell>
										{shift.stop_time ? formatDateTime(shift.stop_time) : 'In Progress'}
									</TableBodyCell>
									<TableBodyCell>
										{#if shift.stop_time}
											{(() => {
												const start = new Date(shift.start_time);
												const end = new Date(shift.stop_time);
												const diff = Math.floor((end.getTime() - start.getTime()) / 1000);

												const hours = Math.floor(diff / 3600);
												const minutes = Math.floor((diff % 3600) / 60);
												const seconds = diff % 60;

												return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
											})()}
										{:else}
											-
										{/if}
									</TableBodyCell>
								</TableBodyRow>
							{/each}
						</TableBody>
					</Table>
				</div>
			{/if}
		</div>
	</div>
</div>
