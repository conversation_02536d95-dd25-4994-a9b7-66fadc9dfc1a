.pilots-tags-container {
	height: 0;
	overflow: hidden;
	transition: height 0.2s ease-in-out;
}

.pilots-tags-container.has-pilots {
	height: auto;
}

.pilots-tags-area {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background-color: rgba(255, 255, 255, 0.8);
}

.pilots-tags-wrapper {
	margin-bottom: 8px;
	padding: 2px;
	max-height: calc(1.5rem * 2 + 0.5rem);
	overflow-y: auto;
}

.pilots-tags-wrapper::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.pilots-tags-wrapper::-webkit-scrollbar-track {
	background: rgba(0, 0, 0, 0.05);
	border-radius: 10px;
}

.pilots-tags-wrapper::-webkit-scrollbar-thumb {
	background-color: var(--color-primary-300);
	border-radius: 10px;
}

.pilots-tags-wrapper::-webkit-scrollbar-thumb:hover {
	background-color: var(--color-primary-400);
}

.pilots-tags-wrapper span {
	margin-bottom: 0.25rem;
	white-space: nowrap;
	transition: all 0.2s ease;
	border: 1px solid var(--color-blue-200);
}

.pilots-tags-wrapper span:hover {
	background-color: var(--color-blue-200);
	transform: translateY(-1px);
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pilots-select-container {
	box-shadow:
		0 1px 3px rgba(0, 0, 0, 0.1),
		0 1px 2px rgba(0, 0, 0, 0.06);
	border-radius: 6px;
	overflow: hidden;
}

@keyframes slide-up {
	from {
		transform: translateY(20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
