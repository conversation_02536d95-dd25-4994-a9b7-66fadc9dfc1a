<script lang="ts">
	import { Modal, Label, Input, Button, Select } from 'flowbite-svelte';
	import { useMutation, useQuery, useQueryClient } from '@sveltestack/svelte-query';
	import { fetchSoftwareVersions, createSoftwareVersion } from '$services/software.service';

	export let showModal: boolean;

	const queryClient = useQueryClient();

	const versionsQuery = useQuery(['software-versions'], fetchSoftwareVersions, {
		select: (data) => {
			const uniqueTypeIds = new Set();
			const uniqueTypes = [];

			for (const item of data) {
				const typeId = item.softwareVersionType.id;
				if (!uniqueTypeIds.has(typeId)) {
					uniqueTypeIds.add(typeId);
					uniqueTypes.push({
						id: typeId,
						name: item.softwareVersionType.name
					});
				}
			}

			return uniqueTypes;
		}
	});

	let formData = {
		name: '',
		softwareVersionTypeId: null
	};

	const addVersionMutation = useMutation(
		async () => {
			if (!formData.softwareVersionTypeId) {
				throw new Error('No version type selected');
			}

			try {
				const result = await createSoftwareVersion({
					name: formData.name,
					softwareVersionTypeId: parseInt(formData.softwareVersionTypeId)
				});
				return result;
			} catch (error) {
				console.error('Error calling API:', error);
				throw error;
			}
		},
		{
			onSuccess: (data) => {
				queryClient.invalidateQueries(['software-versions']);
				showModal = false;
				resetForm();
			},
			onError: (error) => {
				console.error('Mutation failed:', error);
				alert('Failed to create software version: ' + error.message);
			}
		}
	);

	function resetForm() {
		formData.name = '';
		formData.softwareVersionTypeId = null;
	}

	function submitForm(event) {
		event.preventDefault();
		if (!formData.name || !formData.softwareVersionTypeId) {
			console.error('Form data incomplete:', formData);
			alert('Please fill all required fields');
			return;
		}

		try {
			$addVersionMutation.mutate();
		} catch (error) {
			console.error('Mutation error:', error);
		}
	}
</script>

<Modal bind:open={showModal} size="md" autoclose={false}>
	<div class="p-4">
		<h3 class="mb-4 text-xl font-medium text-gray-900">Add New Version</h3>

		<form class="space-y-6" on:submit={submitForm}>
			<div>
				<Label for="versionType" class="mb-2">Version Type</Label>
				<Select id="versionType" required bind:value={formData.softwareVersionTypeId}>
					<option value="" disabled selected>Select version type</option>
					{#if $versionsQuery.data}
						{#each $versionsQuery.data as type}
							<option value={type.id}>{type.name}</option>
						{/each}
					{/if}
				</Select>
			</div>

			<div>
				<Label for="version" class="mb-2">Version</Label>
				<Input id="version" required bind:value={formData.name} placeholder="version number" />
			</div>

			<div class="flex justify-end gap-4">
				<Button color="alternative" on:click={() => (showModal = false)}>Cancel</Button>
				<Button type="submit" color="primary" disabled={$addVersionMutation.isLoading}>
					{$addVersionMutation.isLoading ? 'Saving...' : 'Save'}
				</Button>
			</div>
		</form>
	</div>
</Modal>
