<script lang="ts">
	import { Modal, Label, Input, Button } from 'flowbite-svelte';
	import { updateGlider, type Glider } from '$lib/services/gliders.service';

	export let showModal: boolean = false;
	export let drone: Glider | null = null;
	export let onClose: () => void = () => {};
	export let onSuccess: () => void = () => {};

	// Form data
	let formData = {
		name: '',
		pixhawkUuid: '',
		region: '',
		manufacturingDate: ''
	};

	// Form state
	let saving = false;
	let errors: Record<string, string> = {};

	// Watch for drone changes to populate form
	$: if (drone && showModal) {
		populateForm();
	}

	function populateForm() {
		if (!drone) return;

		formData = {
			name: drone.name || '',
			pixhawkUuid: drone.pixhawkUuid || '',
			region: typeof drone.region === 'object' ? drone.region?.name || '' : drone.region || '',
			manufacturingDate: drone.manufacturingDate ? formatDateForInput(drone.manufacturingDate) : ''
		};
		errors = {};
	}

	function formatDateForInput(dateString: string): string {
		if (!dateString) return '';
		try {
			const date = new Date(dateString);
			return date.toISOString().split('T')[0]; // YYYY-MM-DD format
		} catch {
			return '';
		}
	}

	function validateForm(): boolean {
		errors = {};
		let isValid = true;

		if (!formData.name.trim()) {
			errors.name = 'Drone name is required';
			isValid = false;
		}

		if (formData.manufacturingDate && !isValidDate(formData.manufacturingDate)) {
			errors.manufacturingDate = 'Please enter a valid date';
			isValid = false;
		}

		return isValid;
	}

	function isValidDate(dateString: string): boolean {
		const date = new Date(dateString);
		return !isNaN(date.getTime());
	}

	function hasError(field: string): boolean {
		return field in errors;
	}

	async function handleSubmit() {
		if (!drone || !validateForm()) return;

		try {
			saving = true;
			errors = {};

			// Prepare update data
			const updateData: Partial<Glider> = {
				name: formData.name.trim(),
				pixhawkUuid: formData.pixhawkUuid.trim() || null,
				region: formData.region.trim() || null,
				manufacturingDate: formData.manufacturingDate || null
			};

			// Remove empty values
			Object.keys(updateData).forEach((key) => {
				if (updateData[key as keyof typeof updateData] === '') {
					delete updateData[key as keyof typeof updateData];
				}
			});

			const result = await updateGlider(drone.id, updateData);

			if (result) {
				onSuccess();
				resetAndClose();
			} else {
				errors.general = 'Failed to update drone information. Please try again.';
			}
		} catch (err) {
			console.error('Error updating drone:', err);
			errors.general = err instanceof Error ? err.message : 'Unknown error occurred';
		} finally {
			saving = false;
		}
	}

	function resetAndClose() {
		formData = {
			name: '',
			pixhawkUuid: '',
			region: '',
			manufacturingDate: ''
		};
		errors = {};
		saving = false;
		onClose();
	}

	function handleCancel() {
		resetAndClose();
	}
</script>

<Modal bind:open={showModal} size="md" autoclose={false}>
	<div class="p-6">
		<h3 class="mb-6 text-xl font-semibold text-gray-900">Edit Drone Information</h3>

		{#if errors.general}
			<div class="mb-4 rounded-lg border border-red-200 bg-red-50 p-4">
				<p class="text-sm text-red-600">{errors.general}</p>
			</div>
		{/if}

		<form class="space-y-4" on:submit|preventDefault={handleSubmit}>
			<!-- Drone Name -->
			<div>
				<Label for="drone_name" class="mb-2">Drone Name *</Label>
				<Input
					id="drone_name"
					bind:value={formData.name}
					disabled={saving}
					class={hasError('name') ? 'border-red-500' : ''}
					placeholder="Enter drone name (e.g., M24-12)"
					required
				/>
				{#if hasError('name')}
					<p class="mt-1 text-sm text-red-600">{errors.name}</p>
				{/if}
			</div>

			<!-- Pixhawk UUID -->
			<div>
				<Label for="pixhawk_uuid" class="mb-2">Pixhawk UUID</Label>
				<Input
					id="pixhawk_uuid"
					bind:value={formData.pixhawkUuid}
					disabled={saving}
					class={hasError('pixhawkUuid') ? 'border-red-500' : ''}
					placeholder="Enter Pixhawk UUID"
				/>
				{#if hasError('pixhawkUuid')}
					<p class="mt-1 text-sm text-red-600">{errors.pixhawkUuid}</p>
				{/if}
			</div>

			<!-- Region -->
			<div>
				<Label for="region" class="mb-2">Region</Label>
				<Input
					id="region"
					bind:value={formData.region}
					disabled={saving}
					class={hasError('region') ? 'border-red-500' : ''}
					placeholder="Enter region"
				/>
				{#if hasError('region')}
					<p class="mt-1 text-sm text-red-600">{errors.region}</p>
				{/if}
			</div>

			<!-- Manufacturing Date -->
			<div>
				<Label for="manufacturing_date" class="mb-2">Manufacturing Date</Label>
				<Input
					id="manufacturing_date"
					type="date"
					bind:value={formData.manufacturingDate}
					disabled={saving}
					class={hasError('manufacturingDate') ? 'border-red-500' : ''}
				/>
				{#if hasError('manufacturingDate')}
					<p class="mt-1 text-sm text-red-600">{errors.manufacturingDate}</p>
				{/if}
			</div>

			<!-- Action Buttons -->
			<div class="flex justify-end gap-3 pt-4">
				<Button color="alternative" on:click={handleCancel} disabled={saving}>Cancel</Button>
				<Button type="submit" color="primary" disabled={saving}>
					{saving ? 'Saving...' : 'Save Changes'}
				</Button>
			</div>
		</form>
	</div>
</Modal>
