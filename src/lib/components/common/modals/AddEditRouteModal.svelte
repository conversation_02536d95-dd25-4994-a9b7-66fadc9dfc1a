<script lang="ts">
	import { Modal, Label, Input, Button, Select } from 'flowbite-svelte';
	import { fetchLocations, type Location } from '$lib/services/locations.service';
	import { fetchCustomers, type Customer } from '$lib/services/customers.service';
	import { get } from 'svelte/store';
	import { keycloakClient } from '$lib/stores';
	import { environment } from '$lib/environment';
	import { onMount } from 'svelte';

	export let showModal: boolean;
	export let onClose: () => void;

	let locations: Location[] = [];
	let customers: Customer[] = [];
	let loading = false;

	let formData = {
		start_location_id: '',
		end_location_id: '',
		start_location_name: '',
		end_location_name: '',
		emergency_contact: '',
		known_dangers: '',
		extra_notes: '',
		external_route_id: '',
		customer_id: ''
	};

	let errors: {
		[key: string]: string;
	} = {};

	onMount(async () => {
		try {
			loading = true;
			const [locationsData, customersData] = await Promise.all([
				fetchLocations(),
				fetchCustomers()
			]);

			locations = locationsData;
			customers = customersData;
		} catch (error) {
			console.error('Error fetching data:', error);
		} finally {
			loading = false;
		}
	});

	const resetForm = () => {
		formData = {
			start_location_id: '',
			end_location_id: '',
			start_location_name: '',
			end_location_name: '',
			emergency_contact: '',
			known_dangers: '',
			extra_notes: '',
			external_route_id: '',
			customer_id: ''
		};
		errors = {};
	};

	const handleStartLocationChange = (event: Event) => {
		const select = event.target as HTMLSelectElement;
		const selectedId = select.value;

		formData.start_location_id = selectedId;
		const selectedLocation = locations.find((loc) => loc.id.toString() === selectedId);
		if (selectedLocation) {
			formData.start_location_name = selectedLocation.name;
		}
	};
	const handleEndLocationChange = (event: Event) => {
		const select = event.target as HTMLSelectElement;
		const selectedId = select.value;

		formData.end_location_id = selectedId;
		const selectedLocation = locations.find((loc) => loc.id.toString() === selectedId);
		if (selectedLocation) {
			formData.end_location_name = selectedLocation.name;
		}
	};

	const handleCustomerChange = (event: Event) => {
		const select = event.target as HTMLSelectElement;
		formData.customer_id = select.value;
	};

	const handleSubmit = async () => {
		try {
			const keycloak = get(keycloakClient);
			const token = keycloak?.token;
			if (!token) {
				return;
			}

			const url = `${environment.urlMsRides}/routes`;
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					Authorization: `Bearer ${token}`,
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify(formData)
			});

			if (!response.ok) {
				const errorData = await response.json();
				if (errorData.detail && Array.isArray(errorData.detail)) {
					errors = {};
					errorData.detail.forEach((error) => {
						if (error.loc && error.loc.length > 1) {
							const fieldName = error.loc[1];
							errors[fieldName] = error.msg;
						}
					});
					return;
				}
				throw new Error('Error saving route');
			}

			resetForm();
			onClose();
		} catch (err) {
			console.error('Error creating route:', err);
		}
	};

	const hasError = (field: string): boolean => {
		return !!errors[field];
	};
</script>

<Modal bind:open={showModal} size="md" autoclose={false}>
	<div class="p-4">
		<h3 class="mb-4 text-xl font-medium text-gray-900">Add New Route</h3>
		<form class="space-y-6" on:submit|preventDefault={handleSubmit}>
			<div>
				<Label for="external_route_id" class="mb-2">External Route ID</Label>
				<Input
					id="external_route_id"
					required
					bind:value={formData.external_route_id}
					class={hasError('external_route_id') ? 'border-red-500' : ''}
					placeholder="Enter external route ID"
				/>
				{#if hasError('external_route_id')}
					<p class="mt-1 text-sm text-red-600">{errors['external_route_id']}</p>
				{/if}
				<p class="mt-1 text-xs text-gray-500">
					Unique identifier for this route from external documentation
				</p>
			</div>
			<div>
				<Label for="customer_id" class="mb-2">Customer</Label>
				<Select
					id="customer_id"
					class={hasError('customer_id') ? 'border-red-500' : ''}
					on:change={handleCustomerChange}
					required
				>
					<option value="">Select customer</option>
					{#each customers as customer}
						<option value={customer.id}>{customer.name}</option>
					{/each}
				</Select>
				{#if hasError('customer_id')}
					<p class="mt-1 text-sm text-red-600">{errors['customer_id']}</p>
				{/if}
			</div>
			<div>
				<Label for="start_location_id" class="mb-2">Start Location</Label>
				<Select
					id="start_location_id"
					class={hasError('start_location_id') ? 'border-red-500' : ''}
					on:change={handleStartLocationChange}
					required
				>
					<option value="">Select start location</option>
					{#each locations as location}
						<option value={location.id}>{location.name}</option>
					{/each}
				</Select>
				{#if hasError('start_location_id')}
					<p class="mt-1 text-sm text-red-600">{errors['start_location_id']}</p>
				{/if}
			</div>
			<div>
				<Label for="end_location_id" class="mb-2">End Location</Label>
				<Select
					id="end_location_id"
					class={hasError('end_location_id') ? 'border-red-500' : ''}
					on:change={handleEndLocationChange}
					required
				>
					<option value="">Select end location</option>
					{#each locations as location}
						<option value={location.id}>{location.name}</option>
					{/each}
				</Select>
				{#if hasError('end_location_id')}
					<p class="mt-1 text-sm text-red-600">{errors['end_location_id']}</p>
				{/if}
			</div>
			<div>
				<Label for="emergency_contact" class="mb-2">Emergency Contact</Label>
				<Input
					id="emergency_contact"
					required
					bind:value={formData.emergency_contact}
					placeholder="Emergency Contact"
				/>
			</div>
			<div>
				<Label for="known_dangers" class="mb-2">Known Dangers</Label>
				<Input id="known_dangers" bind:value={formData.known_dangers} placeholder="Known Dangers" />
			</div>
			<div>
				<Label for="extra_notes" class="mb-2">Extra Notes</Label>
				<Input id="extra_notes" bind:value={formData.extra_notes} placeholder="Extra Notes" />
			</div>
			<div class="flex justify-end gap-4">
				<Button
					color="alternative"
					on:click={() => {
						resetForm();
						onClose();
					}}>Cancel</Button
				>
				<Button type="submit" color="primary">Add</Button>
			</div>
		</form>
	</div>
</Modal>
