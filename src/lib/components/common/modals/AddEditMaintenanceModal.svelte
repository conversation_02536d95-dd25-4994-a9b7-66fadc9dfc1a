<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Modal, Label, Input, Button, Select, Textarea, Checkbox } from 'flowbite-svelte';
	import {
		fetchGliders,
		createMaintenance,
		updateMaintenance,
		compareEquipmentNames
	} from '$lib/services/maintenance.service';
	import { fetchMaintenanceTypes } from '$lib/services/maintenance-types.service';
	import { fetchUsers } from '$lib/services/users.service';
	import { fetchSoftwareVersions } from '$lib/services/software.service';
	import { userProfile } from '$lib/stores';
	import type { Maintenance, Equipment, MaintenanceType } from '$lib/types/maintenance.types';
	import type { UserData } from '$lib/services/users.service';
	import type { Software } from '$lib/types/software.types';

	export let showModal: boolean;
	export let maintenance: Maintenance | null = null;

	const dispatch = createEventDispatcher();

	let gliders: Equipment[] = [];
	let maintenanceTypes: MaintenanceType[] = [];
	let staff: UserData[] = [];
	let jetsonVersions: Software[] = [];
	let autopilotVersions: Software[] = [];
	let ftsPixhawkVersions: Software[] = [];
	let ftsRaspiVersions: Software[] = [];
	let loading = false;
	let submitting = false;

	let formData = {
		gliderId: '',
		dueDateTime: '',
		completedDateTime: '',
		maintenanceTypeId: '',
		maintenanceStaff: '',
		notes: '',
		postMaintenanceChecklistDone: false,
		isSoftwareUpdate: false,
		jetsonVersionId: '',
		autopilotVersionId: '',
		ftsPixhawkVersionId: '',
		ftsRaspiVersionId: ''
	};

	let errors: { [key: string]: string } = {};

	$: isEdit = !!maintenance;
	$: modalTitle = isEdit ? 'Edit Maintenance' : 'Add New Maintenance';
	$: sortedGliders = [...gliders].sort((a, b) => compareEquipmentNames(a.name || '', b.name || ''));

	let modalInitialized = false;

	$: if (showModal && !modalInitialized) {
		modalInitialized = true;
		initializeModal();
	} else if (!showModal) {
		modalInitialized = false;
		errors = {};
	}

	onMount(() => {
		loadFormData();
	});

	async function initializeModal() {
		await loadFormData();
		if (isEdit && maintenance) {
			populateForm();
		} else {
			resetForm();
			setDefaults();
			// Set default maintenance type after form reset
			setDefaultMaintenanceType();
		}
	}

	async function loadFormData() {
		loading = true;
		try {
			const [glidersData, typesData, staffData, allSoftwareData] = await Promise.all([
				fetchGliders(),
				fetchMaintenanceTypes(),
				fetchUsers(),
				fetchSoftwareVersions()
			]);

			gliders = glidersData;
			maintenanceTypes = typesData;
			staff = staffData;

			const softwareByType = {};
			allSoftwareData.forEach((version) => {
				const typeName = version.softwareVersionType?.name?.toLowerCase();
				if (typeName) {
					if (!softwareByType[typeName]) {
						softwareByType[typeName] = [];
					}
					softwareByType[typeName].push(version);
				}
			});

			jetsonVersions = findVersionsByKeywords(softwareByType, ['jetson']);
			autopilotVersions = findVersionsByKeywords(softwareByType, ['autopilot']);
			ftsPixhawkVersions = findVersionsByKeywords(softwareByType, [
				'pixhawk',
				'fts-pixhawk',
				'fts_pixhawk'
			]);
			ftsRaspiVersions = findVersionsByKeywords(softwareByType, [
				'raspi',
				'fts-raspi',
				'fts_raspi',
				'raspberry'
			]);

			// Set default maintenance type after data is loaded
			setDefaultMaintenanceType();
		} catch {
		} finally {
			loading = false;
		}
	}

	function setDefaults() {
		formData.dueDateTime = new Date().toISOString().split('T')[0];
		formData.completedDateTime = new Date().toISOString().split('T')[0];

		if ($userProfile?.email) {
			formData.maintenanceStaff = $userProfile.email;
		}
	}

	function setDefaultMaintenanceType() {
		if (maintenanceTypes.length > 0) {
			const unscheduledType = maintenanceTypes.find(
				(type) => type.name.toLowerCase() === 'unscheduled'
			);
			if (unscheduledType) {
				formData.maintenanceTypeId = unscheduledType.id;
			}
		}
	}

	function populateForm() {
		if (!maintenance) return;

		const dueDateTime = maintenance.dueDateTime
			? maintenance.dueDateTime.split('T')[0]
			: new Date().toISOString().split('T')[0];

		const completedDateTime = maintenance.completedDateTime
			? maintenance.completedDateTime.split('T')[0]
			: '';

		const gliderId =
			maintenance.glider?.id || maintenance.gliderId || maintenance.equipmentId || '';

		const maintenanceTypeId =
			maintenance.maintenanceType?.id || maintenance.maintenanceTypeId || '';

		const maintenanceStaff = maintenance.maintenanceStaff || $userProfile?.email || '';

		formData = {
			gliderId: gliderId || '',
			dueDateTime: dueDateTime,
			completedDateTime: completedDateTime,
			maintenanceTypeId: maintenanceTypeId || '',
			maintenanceStaff: maintenanceStaff,
			notes: maintenance.notes || '',
			postMaintenanceChecklistDone: maintenance.postMaintenanceChecklistDone === true,
			isSoftwareUpdate: maintenance.isSoftwareUpdate === true,
			jetsonVersionId: maintenance.glider?.jetsonSoftwareVersion?.id || '',
			autopilotVersionId: maintenance.glider?.autopilotSoftwareVersion?.id || '',
			ftsPixhawkVersionId: maintenance.glider?.ftsPixhawkSoftwareVersion?.id || '',
			ftsRaspiVersionId: maintenance.glider?.ftsRaspiSoftwareVersion?.id || ''
		};
	}

	function validateForm(): boolean {
		errors = {};

		if (!formData.gliderId) {
			errors.gliderId = 'Equipment is required';
		}
		if (!formData.dueDateTime) {
			errors.dueDateTime = 'Due date is required';
		}
		if (!formData.maintenanceTypeId) {
			errors.maintenanceTypeId = 'Maintenance type is required';
		}
		if (!formData.maintenanceStaff) {
			errors.maintenanceStaff = 'Maintenance staff is required';
		}
		if (!formData.notes.trim()) {
			errors.notes = 'Notes are required';
		}
		if (formData.notes.length > 2000) {
			errors.notes = 'Notes must be less than 2000 characters';
		}

		return Object.keys(errors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		submitting = true;
		try {
			const requestData: Record<string, unknown> = {
				dueDateTime: formData.dueDateTime + 'T00:00:00.000Z',
				notes: formData.notes.trim(),
				maintenanceStaff: formData.maintenanceStaff,
				postMaintenanceChecklistDone: formData.postMaintenanceChecklistDone,
				gliderId: Number(formData.gliderId),
				mailboxId: null,
				maintenanceTypeId: Number(formData.maintenanceTypeId)
			};

			if (formData.completedDateTime) {
				requestData.completedDateTime = formData.completedDateTime + 'T00:00:00.000Z';
			}

			let result;
			if (isEdit && maintenance) {
				result = await updateMaintenance(maintenance.id, requestData);
			} else {
				result = await createMaintenance(requestData);
			}

			if (result) {
				showModal = false;
				dispatch('maintenanceCreated');
			} else {
				errors.general = 'Failed to save maintenance record';
			}
		} catch {
			errors.general = 'An error occurred while saving';
		} finally {
			submitting = false;
		}
	}

	function resetForm() {
		formData = {
			gliderId: '',
			dueDateTime: '',
			completedDateTime: '',
			maintenanceTypeId: '',
			maintenanceStaff: '',
			notes: '',
			postMaintenanceChecklistDone: false,
			isSoftwareUpdate: false,
			jetsonVersionId: '',
			autopilotVersionId: '',
			ftsPixhawkVersionId: '',
			ftsRaspiVersionId: ''
		};
		errors = {};
	}

	function handleClose() {
		resetForm();
		dispatch('close');
	}

	function hasError(field: string): boolean {
		return !!errors[field];
	}

	function handleSoftwareUpdateToggle() {
		if (!formData.isSoftwareUpdate) {
			formData.jetsonVersionId = '';
			formData.autopilotVersionId = '';
			formData.ftsPixhawkVersionId = '';
			formData.ftsRaspiVersionId = '';
			formData.notes = formData.notes
				.replace(/Software update according to TSK-ENV002:[\s\S]*?(?=\n\n|$)/g, '')
				.trim();
		}
	}

	let notesTimeout: ReturnType<typeof setTimeout>;

	function findVersionsByKeywords(
		softwareByType: Record<string, Software[]>,
		keywords: string[]
	): Software[] {
		for (const keyword of keywords) {
			if (softwareByType[keyword]) {
				return softwareByType[keyword];
			}
		}
		return [];
	}

	function generateSoftwareUpdateNotes() {
		if (!formData.isSoftwareUpdate) return;
		if (notesTimeout) {
			clearTimeout(notesTimeout);
		}
		notesTimeout = setTimeout(() => {
			const updates = [];

			if (formData.jetsonVersionId) {
				const version = jetsonVersions.find((v) => v.id === Number(formData.jetsonVersionId));
				if (version) updates.push(`Jetson SW to ${version.name}`);
			}
			if (formData.autopilotVersionId) {
				const version = autopilotVersions.find((v) => v.id === Number(formData.autopilotVersionId));
				if (version) updates.push(`Autopilot SW to ${version.name}`);
			}
			if (formData.ftsPixhawkVersionId) {
				const version = ftsPixhawkVersions.find(
					(v) => v.id === Number(formData.ftsPixhawkVersionId)
				);
				if (version) updates.push(`FTS Pixhawk SW to ${version.name}`);
			}
			if (formData.ftsRaspiVersionId) {
				const version = ftsRaspiVersions.find((v) => v.id === Number(formData.ftsRaspiVersionId));
				if (version) updates.push(`FTS Raspi SW to ${version.name}`);
			}

			if (updates.length > 0) {
				const generatedNotes = `Software update according to TSK-ENV002:\n${updates.join('\n')}`;
				const cleanNotes = formData.notes
					.replace(/Software update according to TSK-ENV002:[\s\S]*?(?=\n\n|$)/g, '')
					.trim();
				if (cleanNotes) {
					formData.notes = cleanNotes + '\n\n' + generatedNotes;
				} else {
					formData.notes = generatedNotes;
				}
			}
		}, 100);
	}
</script>

<Modal bind:open={showModal} size="lg" autoclose={false} on:close={handleClose}>
	<div class="p-4">
		<h3 class="mb-4 text-xl font-semibold text-gray-900">{modalTitle}</h3>

		{#if errors.general}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				{errors.general}
			</div>
		{/if}

		<form class="space-y-4" on:submit|preventDefault={handleSubmit}>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
				<div>
					<Label for="equipment" class="mb-2">Equipment *</Label>
					<Select
						id="equipment"
						bind:value={formData.gliderId}
						disabled={loading}
						class={hasError('gliderId') ? 'border-red-500' : ''}
					>
						<option value="">Select Equipment</option>
						{#each sortedGliders as glider}
							<option value={glider.id}>{glider.name}</option>
						{/each}
					</Select>
					{#if hasError('gliderId')}
						<p class="mt-1 text-sm text-red-600">{errors.gliderId}</p>
					{/if}
				</div>
				<div>
					<Label for="type" class="mb-2">Maintenance Type *</Label>
					<Select
						id="type"
						bind:value={formData.maintenanceTypeId}
						disabled={loading}
						class={hasError('maintenanceTypeId') ? 'border-red-500' : ''}
					>
						{#each maintenanceTypes as type}
							<option value={type.id}>{type.name}</option>
						{/each}
					</Select>
					{#if hasError('maintenanceTypeId')}
						<p class="mt-1 text-sm text-red-600">{errors.maintenanceTypeId}</p>
					{/if}
				</div>
				<div>
					<Label for="dueDateTime" class="mb-2">Due Date *</Label>
					<Input
						id="dueDateTime"
						type="date"
						bind:value={formData.dueDateTime}
						class={hasError('dueDateTime') ? 'border-red-500' : ''}
					/>
					{#if hasError('dueDateTime')}
						<p class="mt-1 text-sm text-red-600">{errors.dueDateTime}</p>
					{/if}
				</div>
				<div>
					<Label for="completedDateTime" class="mb-2">Completed Date</Label>
					<Input id="completedDateTime" type="date" bind:value={formData.completedDateTime} />
				</div>
			</div>
			<div>
				<Label for="staff" class="mb-2">Maintenance Staff *</Label>
				<Select
					id="staff"
					bind:value={formData.maintenanceStaff}
					disabled={loading}
					class={hasError('maintenanceStaff') ? 'border-red-500' : ''}
				>
					<option value="">Select Staff Member</option>
					{#each staff as member}
						<option value={member.email}
							>{member.firstName} {member.lastName} ({member.email})</option
						>
					{/each}
				</Select>
				{#if hasError('maintenanceStaff')}
					<p class="mt-1 text-sm text-red-600">{errors.maintenanceStaff}</p>
				{/if}
			</div>
			<div>
				<Label for="notes" class="mb-2">Notes *</Label>
				<Textarea
					id="notes"
					rows="3"
					bind:value={formData.notes}
					placeholder="Enter maintenance notes and details..."
					class={hasError('notes') ? 'border-red-500' : ''}
				/>
				{#if hasError('notes')}
					<p class="mt-1 text-sm text-red-600">{errors.notes}</p>
				{/if}
				<p class="mt-1 text-xs text-gray-500">{formData.notes.length}/2000 characters</p>
			</div>
			<div class="grid grid-cols-2 gap-4">
				<Checkbox bind:checked={formData.postMaintenanceChecklistDone}>Post Check</Checkbox>

				<Checkbox bind:checked={formData.isSoftwareUpdate} on:change={handleSoftwareUpdateToggle}>
					Software Update
				</Checkbox>
			</div>
			{#if formData.isSoftwareUpdate}
				<div class="rounded border border-blue-200 p-3">
					<h4 class="mb-3 text-sm font-medium text-blue-900">Software Versions</h4>
					<div class="grid grid-cols-2 gap-3">
						<div>
							<Label for="jetsonVersion" class="mb-2">Jetson Version</Label>
							<Select
								id="jetsonVersion"
								bind:value={formData.jetsonVersionId}
								disabled={loading}
								on:change={generateSoftwareUpdateNotes}
							>
								<option value="">Select Jetson Version</option>
								{#each jetsonVersions as version}
									<option value={version.id}>{version.name}</option>
								{/each}
							</Select>
						</div>

						<div>
							<Label for="autopilotVersion" class="mb-2">Autopilot Version</Label>
							<Select
								id="autopilotVersion"
								bind:value={formData.autopilotVersionId}
								disabled={loading}
								on:change={generateSoftwareUpdateNotes}
							>
								<option value="">Select Autopilot Version</option>
								{#each autopilotVersions as version}
									<option value={version.id}>{version.name}</option>
								{/each}
							</Select>
						</div>

						<div>
							<Label for="ftsPixhawkVersion" class="mb-2">FTS Pixhawk Version</Label>
							<Select
								id="ftsPixhawkVersion"
								bind:value={formData.ftsPixhawkVersionId}
								disabled={loading}
								on:change={generateSoftwareUpdateNotes}
							>
								<option value="">Select FTS Pixhawk Version</option>
								{#each ftsPixhawkVersions as version}
									<option value={version.id}>{version.name}</option>
								{/each}
							</Select>
						</div>

						<div>
							<Label for="ftsRaspiVersion" class="mb-2">FTS Raspi Version</Label>
							<Select
								id="ftsRaspiVersion"
								bind:value={formData.ftsRaspiVersionId}
								disabled={loading}
								on:change={generateSoftwareUpdateNotes}
							>
								<option value="">Select FTS Raspi Version</option>
								{#each ftsRaspiVersions as version}
									<option value={version.id}>{version.name}</option>
								{/each}
							</Select>
						</div>
					</div>
				</div>
			{/if}
			<div class="flex justify-end space-x-3 border-t pt-4">
				<Button color="light" on:click={handleClose} disabled={submitting}>Cancel</Button>
				<Button
					type="submit"
					disabled={submitting || loading}
					class="bg-primary-600 hover:bg-primary-700"
				>
					{#if submitting}
						Saving...
					{:else}
						{isEdit ? 'Update' : 'Create'}
					{/if}
				</Button>
			</div>
		</form>
	</div>
</Modal>
