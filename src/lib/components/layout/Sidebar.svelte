<script lang="ts">
	import { Sidebar, SidebarGroup, SidebarItem } from 'flowbite-svelte';
	import { page } from '$app/stores';
	import { environment } from '$lib/environment';

	$: activeUrl = $page.url.pathname;
</script>

<div class="h-full p-4">
	<Sidebar class="sidebar-container h-full">
		<SidebarGroup>
			<SidebarItem
				label="Home"
				href="/"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="Routes"
				href="/routes"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/routes' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="Time Tracking History"
				href="/shifts"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/shifts' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="Upload Flight Schedule"
				href="/upload-schedule"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/upload-schedule' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>
		</SidebarGroup>

		<SidebarGroup>
			<SidebarItem
				label="Fleet"
				href="/fleet"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/fleet' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="Locations ↗"
				href={`${environment.urlMainApp}/locations`}
				class="rounded hover:bg-primary-100 hover:text-gray-900"
			/>

			<SidebarItem
				label="Maintenance"
				href="/maintenance"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/maintenance' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="Rides"
				href="/rides"
				class={`rounded hover:bg-primary-100 hover:text-gray-900 ${activeUrl === '/rides' ? 'active-link bg-primary-100 text-gray-900' : ''}`}
			/>

			<SidebarItem
				label="IP Connector ↗"
				href={`${environment.urlIpConnector}`}
				class="rounded hover:bg-primary-100 hover:text-gray-900"
			/>
		</SidebarGroup>
	</Sidebar>
</div>

<style>
	:global(.sidebar-container) {
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
	}

	:global(.sidebar-container nav) {
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
	}

	:global(.sidebar-container nav > ul) {
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
	}

	:global(.sidebar-container nav > ul > li) {
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
	}

	:global(.sidebar-container nav > ul > li > a) {
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
		box-sizing: border-box !important;
		white-space: normal !important;
		text-overflow: unset !important;
		line-height: 1.4 !important;
		word-wrap: break-word !important;
		hyphens: auto !important;
	}

	:global(.active-link) {
		position: relative;
		font-weight: 500 !important;
		padding-left: 1rem !important;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
		overflow: visible !important;
		width: 100% !important;
		max-width: 100% !important;
		box-sizing: border-box !important;
		white-space: normal !important;
		text-overflow: unset !important;
		line-height: 1.4 !important;
		word-wrap: break-word !important;
		hyphens: auto !important;
	}

	:global(.active-link::before) {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 4px;
		background-color: #48a851;
		border-radius: 0 2px 2px 0;
		animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	@keyframes slideIn {
		from {
			transform: translateX(-100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}
</style>
