<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import Logo from '../../icons/logo_black.svg';
	import { userProfile, isUserAdmin } from '$lib/stores';
	import { UserProfileService } from '$lib/services/user-profile.service';

	let dropdownOpen = false;
	let dropdownElement: HTMLDivElement;
	let triggerElement: HTMLButtonElement;
	let showCopySuccess = false;
	let mounted = false;

	onMount(async () => {
		mounted = true;
		await UserProfileService.isUserAdmin();
	});

	function toggleDropdown() {
		dropdownOpen = !dropdownOpen;
	}

	function closeDropdown() {
		dropdownOpen = false;
	}

	function handleLogoClick() {
		goto('/');
	}

	async function handleCopyEmail() {
		if (!$userProfile?.email) return;

		try {
			if (navigator.clipboard && window.isSecureContext) {
				await navigator.clipboard.writeText($userProfile.email);
				showCopySuccess = true;
			} else {
				// Clipboard API not available
				showCopySuccess = false;
			}

			setTimeout(() => {
				showCopySuccess = false;
			}, 2000);
		} catch (error) {
			showCopySuccess = false;
		}
		closeDropdown();
	}

	function handleAccountSettings() {
		window.open(UserProfileService.getKeycloakAccountUrl(), '_blank');
		closeDropdown();
	}

	function handleAdminConsole() {
		const baseUrl = UserProfileService.getKeycloakAccountUrl();
		const adminUrl = baseUrl.replace('/account', '/admin/master/console/');
		window.open(adminUrl, '_blank');
		closeDropdown();
	}

	function handleLogout() {
		UserProfileService.logout();
		closeDropdown();
	}

	$: userInitials = UserProfileService.getUserInitials($userProfile);
	$: userDisplayName = UserProfileService.getUserDisplayName($userProfile);

	$: if ($userProfile && mounted) {
		UserProfileService.storeUserDataInLocalStorage($userProfile);
	}
</script>

<header class="header">
	<div class="header__container">
		<div class="header__brand">
			<button
				on:click={handleLogoClick}
				class="logo-button"
				title="Go to home page"
				aria-label="Go to home page"
			>
				<img src={Logo} alt="Company Logo" />
			</button>
		</div>

		{#if $userProfile}
			<div class="user-menu">
				<button
					bind:this={triggerElement}
					class="user-menu__trigger {dropdownOpen ? 'user-menu__trigger--active' : ''}"
					on:click={toggleDropdown}
					title="User menu"
					aria-label="User menu"
					aria-expanded={dropdownOpen}
					aria-haspopup="true"
				>
					<div class="user-menu__avatar">
						{userInitials}
					</div>
					<div class="user-menu__info">
						<div class="user-menu__name">{userDisplayName}</div>
						<div class="user-menu__email">{$userProfile.email}</div>
					</div>
				</button>

				<div
					bind:this={dropdownElement}
					class="dropdown {dropdownOpen ? 'dropdown--visible' : ''}"
					role="menu"
					aria-labelledby="user-menu-button"
				>
					<div class="dropdown__header">
						<div class="dropdown__avatar-large">
							{userInitials}
						</div>
						<div class="dropdown__user-details">
							<div class="dropdown__user-name">{userDisplayName}</div>
							<div class="dropdown__user-email">{$userProfile.email}</div>
						</div>
					</div>

					<div class="dropdown__divider"></div>

					<div class="dropdown__section">
						<button
							class="menu-item {showCopySuccess ? 'menu-item--success copy-success' : ''}"
							on:click={handleCopyEmail}
							title="Copy email to clipboard"
							role="menuitem"
						>
							<span class="menu-item__text">
								{showCopySuccess ? 'Email Copied!' : 'Copy Email'}
							</span>
						</button>

						<button class="menu-item" on:click={handleAccountSettings} role="menuitem">
							<span class="menu-item__text">Account Settings</span>
						</button>

						{#if $isUserAdmin}
							<button class="menu-item" on:click={handleAdminConsole} role="menuitem">
								<span class="menu-item__text">Admin Console</span>
							</button>
						{/if}
					</div>

					<div class="dropdown__divider"></div>

					<div class="dropdown__section">
						<button class="menu-item menu-item--danger" on:click={handleLogout} role="menuitem">
							<span class="menu-item__text">Logout</span>
						</button>
					</div>
				</div>
			</div>
		{/if}
	</div>
</header>

<style lang="scss">
	.header {
		background: #ffffff;
		border-bottom: 1px solid #e5e7eb;
		box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
		position: sticky;
		top: 0;
		z-index: 50;

		&__container {
			max-width: 1200px;
			margin: 0 auto;
			padding: 1rem 1.5rem;
			display: flex;
			align-items: center;
			justify-content: space-between;

			@media (max-width: 640px) {
				padding: 0.75rem 1rem;
			}
		}

		&__brand {
			display: flex;
			align-items: center;
			gap: 0.75rem;
		}
	}

	.logo-button {
		background: none;
		border: none;
		padding: 0.5rem;
		cursor: pointer;
		transition: transform 0.2s ease;
		border-radius: 0.375rem;

		&:hover {
			transform: scale(1.02);
		}

		&:active {
			transform: scale(0.98);
		}

		&:focus-visible {
			outline: 2px solid #48a851;
			outline-offset: 2px;
		}

		img {
			height: 2.5rem;
			width: auto;

			@media (max-width: 640px) {
				height: 2rem;
			}
		}
	}

	.user-menu {
		position: relative;

		&__trigger {
			background: none;
			border: none;
			padding: 0.25rem 0.5rem;
			cursor: pointer;
			transition: all 0.2s ease;
			display: flex;
			align-items: center;
			gap: 0.5rem;
			border-radius: 0.375rem;
			min-width: 120px;

			&:hover {
				background: #f9fafb;
			}

			&:focus-visible {
				outline: 2px solid #48a851;
				outline-offset: 2px;
			}

			&--active {
				background: #f9fafb;
			}

			@media (max-width: 640px) {
				min-width: 160px;
				gap: 0.5rem;
				padding: 0.25rem 0.5rem;
			}
		}

		&__avatar {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 1.75rem;
			height: 1.75rem;
			border-radius: 50%;
			background: linear-gradient(135deg, #48a851, #358a3d);
			color: #ffffff;
			font-weight: 600;
			font-size: 0.75rem;
			flex-shrink: 0;
			box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

			@media (max-width: 640px) {
				width: 1.5rem;
				height: 1.5rem;
				font-size: 0.625rem;
			}
		}

		&__info {
			flex: 1;
			text-align: left;
			min-width: 0;
		}

		&__name {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-weight: 500;
			color: #1f2937;
			font-size: 0.75rem;
			line-height: 1.1;

			@media (max-width: 640px) {
				font-size: 0.6875rem;
			}
		}

		&__email {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-size: 0.6875rem;
			color: #6b7280;
			line-height: 1.1;
			margin-top: 1px;

			@media (max-width: 640px) {
				font-size: 0.625rem;
			}
		}
	}

	.dropdown {
		position: absolute;
		top: calc(100% + 0.25rem);
		right: 0;
		z-index: 50;
		min-width: 220px;
		background: #ffffff;
		border-radius: 0.5rem;
		box-shadow:
			0 4px 6px -1px rgba(0, 0, 0, 0.1),
			0 2px 4px -1px rgba(0, 0, 0, 0.06);
		overflow: hidden;
		opacity: 0;
		visibility: hidden;
		transform: translateY(-8px) scale(0.95);
		transition: all 0.15s ease;

		&--visible {
			opacity: 1;
			visibility: visible;
			transform: translateY(0) scale(1);
		}

		@media (max-width: 640px) {
			min-width: 200px;
			right: -0.5rem;
		}

		&__header {
			padding: 0.75rem;
			background: #f9fafb;
			display: flex;
			align-items: center;
			gap: 0.75rem;
		}

		&__avatar-large {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 2.5rem;
			height: 2.5rem;
			border-radius: 50%;
			background: linear-gradient(135deg, #48a851, #358a3d);
			color: #ffffff;
			font-weight: 700;
			font-size: 0.875rem;
			flex-shrink: 0;
			box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
		}

		&__user-details {
			flex: 1;
			min-width: 0;
		}

		&__user-name {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-weight: 600;
			color: #1f2937;
			font-size: 0.875rem;
			line-height: 1.2;
			margin-bottom: 1px;
		}

		&__user-email {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-size: 0.75rem;
			color: #6b7280;
			line-height: 1.2;
		}

		&__divider {
			height: 0;
			margin: 0.25rem 0;
		}

		&__section {
			padding: 0.5rem 0;
		}
	}

	.menu-item {
		background: none;
		border: none;
		padding: 0.75rem 1.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
		width: 100%;
		display: flex;
		align-items: center;
		gap: 0.75rem;
		font-size: 0.875rem;
		color: #374151;
		text-align: left;

		&:hover {
			background: #f9fafb;
		}

		&:focus-visible {
			background: #f9fafb;
			outline: 2px solid #48a851;
			outline-offset: -2px;
		}

		&__text {
			flex: 1;
			font-weight: 500;
		}

		&--danger {
			color: #dc2626;

			&:hover {
				background: rgba(239, 68, 68, 0.05);
				color: #b91c1c;
			}
		}

		&--success {
			&.copy-success {
				background: rgba(34, 197, 94, 0.1);
				color: #16a34a;
				animation: copySuccess 0.3s ease;
			}
		}
	}

	@keyframes copySuccess {
		0% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.02);
		}
		100% {
			transform: scale(1);
		}
	}
</style>
