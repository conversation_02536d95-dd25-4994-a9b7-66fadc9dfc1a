import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';
import type {
	Maintenance,
	MaintenanceCreateRequest,
	MaintenanceFilters,
	PaginationConfig,
	Equipment
} from '../types/maintenance.types';

export async function fetchGliders(): Promise<Equipment[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return Array.isArray(data) ? data : [];
	} catch (error) {
		return [];
	}
}

export async function fetchMaintenances(
	filters: MaintenanceFilters = {},
	pagination: PaginationConfig = { page: 1, limit: 10, total: 0 }
): Promise<Maintenance[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const params = new URLSearchParams();

		const skip = (pagination.page - 1) * pagination.limit;
		params.append('skip', skip.toString());
		params.append('limit', pagination.limit.toString());

		if (filters.equipmentId) {
			params.append('equipmentId', filters.equipmentId.toString());
		}
		if (filters.searchTerm) {
			params.append('searchTerm', filters.searchTerm);
		}
		if (filters.dateFrom) {
			params.append('dateFrom', filters.dateFrom);
		}
		if (filters.dateTo) {
			params.append('dateTo', filters.dateTo);
		}
		if (filters.maintenanceTypeId) {
			params.append('maintenanceTypeId', filters.maintenanceTypeId.toString());
		}
		if (filters.isCompleted !== undefined) {
			params.append('isCompleted', filters.isCompleted.toString());
		}

		const url = `${environment.gliderMsBackendUrl}/maintenances?${params.toString()}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data.data || data || [];
	} catch {
		return [];
	}
}

export async function fetchMaintenanceById(id: number): Promise<Maintenance | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/maintenances/${id}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return null;
		}

		const data = await response.json();
		return data;
	} catch {
		return null;
	}
}

export async function createMaintenance(
	data: MaintenanceCreateRequest
): Promise<Maintenance | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/maintenances`;
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Error response:', errorText);
			try {
				const errorJson = JSON.parse(errorText);
				console.error('Parsed error:', errorJson);
			} catch {
				console.error('Could not parse error as JSON');
			}
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch {
		return null;
	}
}

export async function updateMaintenance(
	id: number,
	data: Partial<MaintenanceCreateRequest>
): Promise<Maintenance | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/maintenances/${id}`;

		const updateData: Record<string, unknown> = {
			dueDateTime: data.dueDateTime,
			completedDateTime: data.completedDateTime,
			notes: data.notes,
			postMaintenanceChecklistDone: data.postMaintenanceChecklistDone,
			gliderId: data.gliderId,
			maintenanceTypeId: data.maintenanceTypeId,
			mailboxId: data.mailboxId || null,
			maintenanceStaff: data.maintenanceStaff
		};

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(updateData)
		});

		if (!response.ok) {
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch {
		return null;
	}
}

export async function deleteMaintenance(): Promise<boolean> {
	return true;
}

export function compareEquipmentNames(nameA: string, nameB: string): number {
	const pattern = /^[Mm](\d+)-(\d+)$/;
	const matchA = nameA.match(pattern);
	const matchB = nameB.match(pattern);

	if (matchA && matchB) {
		const xxA = parseInt(matchA[1], 10);
		const yyA = parseInt(matchA[2], 10);
		const xxB = parseInt(matchB[1], 10);
		const yyB = parseInt(matchB[2], 10);

		if (xxA !== xxB) return xxB - xxA;
		return yyB - yyA;
	}

	if (matchA && !matchB) return -1;
	if (!matchA && matchB) return 1;

	return nameA.localeCompare(nameB);
}
