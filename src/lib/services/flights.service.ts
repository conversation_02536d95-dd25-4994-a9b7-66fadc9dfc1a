import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Flight {
	id: number;
	glider_id: number;
	glider_name?: string;
	start_location_id: number;
	start_location_name?: string;
	end_location_id: number;
	end_location_name?: string;
	pilot_id: number;
	pilot_name?: string;
	scheduled_start_time: string;
	scheduled_end_time?: string | null;
	actual_start_time?: string | null;
	actual_end_time?: string | null;
	status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
	notes?: string;
}

export async function fetchFlights(skip = 0, limit = 10) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.urlMsRides}/flights?skip=${skip}&limit=${limit}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch flights:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching flights:', error);
		return [];
	}
}

export async function createFlight(data) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/flights`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to create flight:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error creating flight:', error);
		return null;
	}
}

export async function updateFlight(id, data) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/flights/${id}`;

		const response = await fetch(url, {
			method: 'PUT',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to update flight:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error updating flight:', error);
		return null;
	}
}
