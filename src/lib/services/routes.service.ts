import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Route {
	start_location_id: number;
	start_location_name: string;
	end_location_id: number;
	end_location_name: string;
	emergency_contact: string;
	known_dangers: string;
	extra_notes: string;
	external_route_id: string;
	id: number;
	customer: {
		name: string;
		id: number;
	};
}

export async function fetchRoutes(skip: number = 0, limit: number = 1000): Promise<Route[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.urlMsRides}/routes?skip=${skip}&limit=${limit}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			try {
				const errorText = await response.text();
				console.error('Error response text:', errorText);
			} catch (error) {
				console.error('Could not read error response text');
			}

			return [];
		}

		return await response.json();
	} catch (error) {
		console.error('Error fetching routes:', error);
		return [];
	}
}

export async function fetchRouteById(id: number): Promise<Route | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/routes/${id}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch route:', response.status, response.statusText);
			return null;
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching route:', error);
		return null;
	}
}

export async function updateRoute(id: number, data: Partial<Route>): Promise<Route | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/routes/${id}`;

		const response = await fetch(url, {
			method: 'PUT',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to update route:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error updating route:', error);
		return null;
	}
}

export async function createRoute(data: Omit<Route, 'id'>): Promise<Route | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/routes`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to create route:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		return null;
	}
}
