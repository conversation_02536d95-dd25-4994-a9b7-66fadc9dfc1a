import { get } from 'svelte/store';
import { keycloakClient } from '$lib/stores';
import { environment } from '$lib/environment';

interface Software {
	id: number;
	name: string;
	version: string;
	createdAt: string;
	updatedAt: string;
	softwareVersionType: {
		id: number;
		name: string;
	};
}

interface CreateSoftwareVersionDto {
	name: string;
	softwareVersionTypeId: number;
}

interface KeycloakInstance {
	token?: string;
}

export async function fetchSoftwareVersions(): Promise<Software[]> {
	const keycloak = get(keycloakClient) as KeycloakInstance | null;
	const token = keycloak?.token;

	if (!token) {
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/software-versions`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		if (!response.ok) {
			return [];
		}

		return await response.json();
	} catch (error) {
		return [];
	}
}

export async function fetchSoftwareByType(type: string): Promise<Software[]> {
	const allVersions = await fetchSoftwareVersions();

	const searchKeywords = type.toLowerCase().split('-').concat([type.toLowerCase()]);

	return allVersions.filter((version) => {
		const versionTypeName = version.softwareVersionType?.name?.toLowerCase();
		if (!versionTypeName) return false;

		return searchKeywords.some(
			(keyword) => versionTypeName.includes(keyword) || keyword.includes(versionTypeName)
		);
	});
}

export async function createSoftwareVersion(data: CreateSoftwareVersionDto): Promise<Software> {
	const keycloak = get(keycloakClient) as KeycloakInstance | null; // Припускаємо тип
	const token = keycloak?.token;

	if (!token) {
		throw new Error('Keycloak client or token not found');
	}

	const url = `${environment.gliderMsBackendUrl}/software-versions`;

	if (!data.name) throw new Error('Name is required');
	if (!data.softwareVersionTypeId) throw new Error('Software version type ID is required');

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Response error text:', errorText);

			try {
				const errorData = JSON.parse(errorText);
				console.error('Parsed error data:', errorData);
				throw new Error(errorData?.message || 'Error creating software version');
			} catch (parseError) {
				console.error('Error parsing response:', parseError);
				throw new Error(
					`Error creating software version: ${response.status} ${response.statusText}`
				);
			}
		}

		return await response.json();
	} catch (error) {
		console.error('Fetch error:', error);
		throw error;
	}
}
