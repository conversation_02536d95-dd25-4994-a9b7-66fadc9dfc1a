import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Location {
	id: number;
	name: string;
	pictureUrl: string;
	videoUrl: string;
	latitude: number | null;
	longitude: number | null;
	radius: number | null;
	locationCategory: {
		id: number;
		name: string;
		description: string;
		createdAt: string;
		updatedAt: string;
	} | null;
	locationStatus: {
		id: number;
		name: string;
		description: string;
		createdAt: string;
		updatedAt: string;
	} | null;
	createdAt: string;
	updatedAt: string;
}

export async function fetchLocations(): Promise<Location[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/locations`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return [];
	}
}
