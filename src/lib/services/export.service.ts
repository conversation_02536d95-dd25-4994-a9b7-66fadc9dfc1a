import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface ExportParams {
	start_date?: string;
	end_date?: string;
	pilot_email?: string[];
	route_id?: number;
	customer_id?: number;
}

export interface RidesExportParams {
	operator_id?: string;
	ride_status_id?: number;
	glider_name?: string;
	route_id?: number;
	start_time?: string;
	end_time?: string;
	customer_id?: number;
}

export const exportShiftsCSV = async (params: ExportParams): Promise<void> => {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token as string | undefined;

	if (!token) {
		throw new Error('Authentication token not found');
	}

	const queryParams = new URLSearchParams();

	if (params.start_date) {
		queryParams.append('start_date', params.start_date);
	}
	if (params.end_date) {
		queryParams.append('end_date', params.end_date);
	}
	if (params.route_id) {
		queryParams.append('route_id', params.route_id.toString());
	}
	if (params.customer_id) {
		queryParams.append('customer_id', params.customer_id.toString());
	}
	if (params.pilot_email) {
		params.pilot_email.forEach((email) => {
			queryParams.append('pilot_email', email);
		});
	}

	const url = `${environment.urlMsRides}/csv-export/shifts?${queryParams.toString()}`;

	const response = await fetch(url, {
		method: 'GET',
		headers: {
			Authorization: `Bearer ${token}`,
			Accept: 'text/csv'
		}
	});

	if (!response.ok) {
		let errorMessage = `Failed to export CSV: ${response.status} ${response.statusText}`;
		try {
			const errorData = await response.json();
			if (errorData.detail) {
				errorMessage = `Error: ${errorData.detail}`;
			}
		} catch (_) {}
		throw new Error(errorMessage);
	}

	const blob = await response.blob();
	const downloadUrl = window.URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = downloadUrl;
	link.download = `shifts_export_${new Date().toISOString().split('T')[0]}.csv`;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	window.URL.revokeObjectURL(downloadUrl);
};

export const exportRidesCSV = async (params: RidesExportParams): Promise<void> => {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token as string | undefined;

	if (!token) {
		throw new Error('Authentication token not found');
	}

	const queryParams = new URLSearchParams();

	if (params.operator_id) {
		queryParams.append('operator_id', params.operator_id);
	}
	if (params.ride_status_id) {
		queryParams.append('ride_status_id', params.ride_status_id.toString());
	}
	if (params.glider_name) {
		queryParams.append('glider_name', params.glider_name);
	}
	if (params.route_id) {
		queryParams.append('route_id', params.route_id.toString());
	}
	if (params.start_time) {
		queryParams.append('start_time', params.start_time);
	}
	if (params.end_time) {
		queryParams.append('end_time', params.end_time);
	}
	if (params.customer_id) {
		queryParams.append('customer_id', params.customer_id.toString());
	}

	const url = `${environment.urlMsRides}/csv-export/rides?${queryParams.toString()}`;

	const response = await fetch(url, {
		method: 'GET',
		headers: {
			Authorization: `Bearer ${token}`,
			Accept: 'text/csv'
		}
	});

	if (!response.ok) {
		let errorMessage = `Failed to export CSV: ${response.status} ${response.statusText}`;
		try {
			const errorData = await response.json();
			if (errorData.detail) {
				errorMessage = `Error: ${errorData.detail}`;
			}
		} catch (_) {}
		throw new Error(errorMessage);
	}

	const blob = await response.blob();
	const downloadUrl = window.URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = downloadUrl;
	link.download = `rides_export_${new Date().toISOString().split('T')[0]}.csv`;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	window.URL.revokeObjectURL(downloadUrl);
};
