import { get } from 'svelte/store';
import { keycloakClient } from '$lib/stores';
import { userProfile } from '$lib/stores';
import { environment } from '$lib/environment';
import { toast } from 'svelte-sonner';

export interface NetworkDevice {
	id: string;
	ipAddress: string;
	email: string;
	fts: boolean; // optional legacy field, kann entfernt werden
	name: string;
	lastOnline: string;
	macAddress: string | null;
	architecture: string;
	certificateExpires: string;
	createdAt: string;
	deviceType: string; // 'drone', 'laptop', etc.
	interfaceRole: 'primary' | 'secondary'; // 'secondary' = FTS bei Dr<PERSON>nen
}

/**
 * Fetch network devices directly from PKI API based on drone name and/or user email.
 * Also merges healthcheck info from healthcheck API.
 */
export async function fetchNetworkDevices(
	droneName: string | null,
	userEmail: string | null
): Promise<NetworkDevice[]> {
	try {
		const kc = get(keycloakClient);
		const token = kc?.token;
		if (!token) {
			return [];
		}

		const profile = get(userProfile);
		const email = userEmail || profile?.email;

		const commonHeaders = {
			Accept: 'application/json',
			Authorization: `Bearer ${token}`
		};

		const endpoints: unknown[] = [];

		if (!droneName && !email) {
			try {
				const allUrl = `${environment.urlPkiApi}/vpn/endpoints`;
				const res = await fetch(allUrl, { headers: commonHeaders });
				if (res.ok) {
					const list = await res.json();
					if (Array.isArray(list)) {
						endpoints.push(...list);
						return endpoints.map((entry: unknown) => {
							const pkiEntry = entry as {
								id: string;
								ip_address: string;
								interface_type: number;
								mac_address?: string;
								architecture?: string;
								expires_at?: string;
								created_at?: string;
								device_type?: string;
								user_email?: string;
							};

							let deviceName = '';
							if (pkiEntry.device_type === 'drone') {
								deviceName = 'unknown-drone';
							} else if (pkiEntry.device_type === 'macbook' || pkiEntry.device_type === 'laptop') {
								deviceName = `${pkiEntry.device_type}-${pkiEntry.ip_address.split('.').pop()}`;
							} else if (pkiEntry.device_type === 'iphone' || pkiEntry.device_type === 'android') {
								deviceName = `${pkiEntry.device_type}-${pkiEntry.ip_address.split('.').pop()}`;
							} else {
								deviceName = pkiEntry.device_type ?? 'unknown';
							}

							const interfaceRole: 'primary' | 'secondary' =
								pkiEntry.interface_type === 2 ? 'secondary' : 'primary';

							const deviceEmail = pkiEntry.user_email || '';

							return {
								id: pkiEntry.id,
								ipAddress: pkiEntry.ip_address,
								email: deviceEmail,
								name: deviceName,
								fts: pkiEntry.interface_type === 2,
								lastOnline: 'unknown',
								macAddress: pkiEntry.mac_address ?? null,
								architecture: pkiEntry.architecture ?? '-',
								certificateExpires: pkiEntry.expires_at ?? '',
								createdAt: pkiEntry.created_at ?? '',
								deviceType: pkiEntry.device_type ?? 'unknown',
								interfaceRole
							};
						});
					}
				}
			} catch (error) {
				const message =
					error instanceof Error ? error.message : 'Failed to fetch all network devices';
				toast.error(message);
			}
		}

		if (droneName) {
			try {
				const lookupUrl = `${environment.urlPkiApi}/vpn/endpoints/lookup?device_name=${droneName}`;
				const res = await fetch(lookupUrl, { headers: commonHeaders });
				if (res.ok) {
					const list = await res.json();
					if (Array.isArray(list)) {
						endpoints.push(...list);
					}
				}
			} catch (error) {
				const message = error instanceof Error ? error.message : 'Failed to fetch drone devices';
				toast.error(message);
			}
		}

		if (email) {
			try {
				const emailUrl = `${environment.urlPkiApi}/vpn/endpoints/user?email=${encodeURIComponent(email)}`;
				const res = await fetch(emailUrl, { headers: commonHeaders });
				if (res.ok) {
					const list = await res.json();
					if (Array.isArray(list)) {
						endpoints.push(...list);
					}
				}
			} catch (error) {
				const message = error instanceof Error ? error.message : 'Failed to fetch user devices';
				toast.error(message);
			}
		}

		const mapped: NetworkDevice[] = endpoints.map((entry: unknown) => {
			const pkiEntry = entry as {
				id: string;
				ip_address: string;
				interface_type: number;
				mac_address?: string;
				architecture?: string;
				expires_at?: string;
				created_at?: string;
				device_type?: string;
				user_email?: string;
			};

			let deviceName = '';
			if (pkiEntry.device_type === 'drone') {
				deviceName = droneName ?? '';
			} else if (pkiEntry.device_type === 'macbook' || pkiEntry.device_type === 'laptop') {
				deviceName = `${pkiEntry.device_type}-${pkiEntry.ip_address.split('.').pop()}`;
			} else if (pkiEntry.device_type === 'iphone' || pkiEntry.device_type === 'android') {
				deviceName = `${pkiEntry.device_type}-${pkiEntry.ip_address.split('.').pop()}`;
			} else {
				deviceName = pkiEntry.device_type ?? 'unknown';
			}

			const interfaceRole: 'primary' | 'secondary' =
				pkiEntry.interface_type === 2 ? 'secondary' : 'primary';

			const deviceEmail = pkiEntry.user_email || email || '';

			return {
				id: pkiEntry.id,
				ipAddress: pkiEntry.ip_address,
				email: deviceEmail,
				name: deviceName,
				fts: pkiEntry.interface_type === 2,
				lastOnline: 'unknown',
				macAddress: pkiEntry.mac_address ?? null,
				architecture: pkiEntry.architecture ?? '-',
				certificateExpires: pkiEntry.expires_at ?? '',
				createdAt: pkiEntry.created_at ?? '',
				deviceType: pkiEntry.device_type ?? 'unknown',
				interfaceRole
			};
		});

		const ipList = mapped.map((d) => d.ipAddress);
		const healthStatus: Record<string, string> = {};

		if (ipList.length > 0) {
			try {
				const healthRes = await fetch(`${environment.urlHealthCheck}/status/by-ip`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(ipList)
				});

				if (healthRes.ok) {
					const healthData = await healthRes.json();
					for (const h of healthData) {
						const cleanIp = h.ip_address.replace('/32', '');
						healthStatus[cleanIp] = h.last_seen_friendly;
					}
				}
			} catch {
				// Continue without health status if healthcheck fails
			}
		}

		const enriched = mapped.map((entry) => ({
			...entry,
			lastOnline: healthStatus[entry.ipAddress] || 'unknown'
		}));

		return enriched;
	} catch {
		return [];
	}
}

/**
 * Get all network devices for a given drone
 */
export async function getNetworkDevicesForDrone(droneName: string): Promise<NetworkDevice[]> {
	return await fetchNetworkDevices(droneName, null);
}

/**
 * Get network device by IP address
 */
export async function getNetworkDeviceByIp(ipAddress: string): Promise<NetworkDevice | null> {
	const devices = await fetchNetworkDevices(null, null);
	return devices.find((device) => device.ipAddress === ipAddress) || null;
}

/**
 * Get all FTS devices (secondary interfaces of drones)
 */
export async function getFtsDevices(): Promise<NetworkDevice[]> {
	const devices = await fetchNetworkDevices(null, null);
	return devices.filter(
		(device) => device.deviceType === 'drone' && device.interfaceRole === 'secondary'
	);
}

/**
 * Get all primary drone devices
 */
export async function getDroneDevices(): Promise<NetworkDevice[]> {
	const devices = await fetchNetworkDevices(null, null);
	return devices.filter(
		(device) => device.deviceType === 'drone' && device.interfaceRole === 'primary'
	);
}

/**
 * Get devices by user email
 */
export async function getDevicesByEmail(email: string): Promise<NetworkDevice[]> {
	return await fetchNetworkDevices(null, email);
}

/**
 * Helper: Get the drone's primary interface
 */
export function findDroneDevice(devices: NetworkDevice[], droneName: string): NetworkDevice | null {
	return (
		devices.find(
			(d) => d.deviceType === 'drone' && d.interfaceRole === 'primary' && d.name.includes(droneName)
		) || null
	);
}

/**
 * Helper: Get the FTS interface of a drone
 */
export function findFtsDevice(devices: NetworkDevice[], droneName: string): NetworkDevice | null {
	return (
		devices.find(
			(d) =>
				d.deviceType === 'drone' && d.interfaceRole === 'secondary' && d.name.includes(droneName)
		) || null
	);
}
