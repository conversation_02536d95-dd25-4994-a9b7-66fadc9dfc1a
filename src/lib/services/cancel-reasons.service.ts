import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface CancelReason {
	id: number;
	name: string;
	description: string;
}

export async function fetchCancelReasons(): Promise<CancelReason[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.urlMsRides}/rides/cancel-reasons`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return [];
	}
}

export async function updateRideCancelReason(
	rideId: number,
	cancelReasonId: number | null
): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify({
				cancel_reason_id: cancelReasonId
			})
		});

		if (!response.ok) {
			console.error('Failed to update cancel reason:', response.status, response.statusText);
			return false;
		}

		return true;
	} catch (error) {
		console.error('Error updating cancel reason:', error);
		return false;
	}
}
