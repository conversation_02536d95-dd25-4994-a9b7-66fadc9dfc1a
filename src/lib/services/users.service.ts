import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface UserData {
	id: string;
	createdTimestamp: number;
	username: string;
	enabled: boolean;
	totp: boolean;
	emailVerified: boolean;
	firstName: string;
	lastName: string;
	email: string;
	attributes?: Record<string, string[]>;
	disableableCredentialTypes: string[];
	requiredActions: string[];
	notBefore: number;
	access: {
		manageGroupMembership: boolean;
		view: boolean;
		mapRoles: boolean;
		impersonate: boolean;
		manage: boolean;
	};
}

let usersCache: UserData[] | null = null;
let lastFetchTime = 0;
const CACHE_EXPIRY_MS = 10 * 60 * 1000;

export async function fetchUsers(forceRefresh = false) {
	const now = Date.now();

	if (!forceRefresh && usersCache && now - lastFetchTime < CACHE_EXPIRY_MS) {
		return usersCache;
	}

	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsAuth}/admin/realms/jedsy/users`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		usersCache = data;
		lastFetchTime = now;
		return data;
	} catch (error) {
		throw new Error('Failed to load users from Keycloak API');
	}
}

export function findPilotByEmailOrName(
	users: UserData[],
	searchValue: string
): UserData | undefined {
	if (!searchValue || !users || users.length === 0) {
		return undefined;
	}
	const normalizedSearch = searchValue.toLowerCase().trim();
	for (const user of users) {
		if (user.email && user.email.toLowerCase() === normalizedSearch) {
			return user;
		}
	}

	for (const user of users) {
		if (user.email && user.email.toLowerCase().indexOf(normalizedSearch) !== -1) {
			return user;
		}
	}

	for (const user of users) {
		const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase().trim();
		const reversedFullName = `${user.lastName || ''} ${user.firstName || ''}`.toLowerCase().trim();

		if (
			(fullName && fullName.indexOf(normalizedSearch) !== -1) ||
			(reversedFullName && reversedFullName.indexOf(normalizedSearch) !== -1)
		) {
			return user;
		}
	}
	return undefined;
}
