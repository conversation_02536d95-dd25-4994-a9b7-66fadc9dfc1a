import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Ride {
	id?: number;
	from_location: number;
	to_location: number;
	departure_time: string;
	arrival_time?: string | null;
	ride_status_id: number;
	glider_id: number;
	glider_name?: string;
	operator_id: string | number;
	has_package: boolean;
	package_description?: string;
	route_id?: number | null;
	cancel_reason_id?: number | null;
}

export async function fetchRides(
	params: {
		skip?: number;
		limit?: number;
		operator_id?: string | null;
		ride_status_id?: number | null;
		glider_name?: string | null;
		route_id?: number | null;
		start_time?: string | null;
		end_time?: string | null;
		customer_id?: number | null;
	} = {}
) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const queryParams = new URLSearchParams();

		queryParams.append('skip', String(params.skip || 0));
		queryParams.append('limit', String(params.limit || 10));

		if (params.operator_id) queryParams.append('operator_id', params.operator_id);
		if (params.ride_status_id) queryParams.append('ride_status_id', String(params.ride_status_id));
		if (params.glider_name) queryParams.append('glider_name', params.glider_name);
		if (params.route_id) queryParams.append('route_id', String(params.route_id));
		if (params.start_time) queryParams.append('start_time', params.start_time);
		if (params.end_time) queryParams.append('end_time', params.end_time);
		if (params.customer_id) queryParams.append('customer_id', String(params.customer_id));

		const url = `${environment.urlMsRides}/rides?${queryParams.toString()}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();

		return data;
	} catch (error) {
		return [];
	}
}

export async function createRide(data: Ride) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.json();
				errorText = errorBody.detail || JSON.stringify(errorBody);
			} catch (e) {}
			throw new Error(errorText);
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Failed to create ride:', error);
		throw error;
	}
}

export async function fetchRideById(rideId: number): Promise<Ride | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch ride:', response.status, response.statusText);
			return null;
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching ride:', error);
		return null;
	}
}

export async function updateRide(rideId: number, data: Partial<Ride>): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.json();
				errorText = errorBody.detail || JSON.stringify(errorBody);
			} catch (e) {}
			throw new Error(errorText);
		}

		return true;
	} catch (error) {
		throw error;
	}
}

export async function deleteRide(rideId: number): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'DELETE',
			headers: {
				Authorization: `Bearer ${token}`,
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.json();
				errorText = errorBody.detail || JSON.stringify(errorBody);
			} catch (e) {}
			throw new Error(errorText);
		}

		return true;
	} catch (error) {
		throw error;
	}
}

export async function updateRidePackageInfo(
	rideId: number,
	hasPackage: boolean,
	packageDescription: string
): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify({
				has_package: hasPackage,
				package_description: hasPackage ? packageDescription : ''
			})
		});

		if (!response.ok) {
			console.error('Failed to update ride package info:', response.status, response.statusText);
			return false;
		}

		return true;
	} catch (error) {
		console.error('Error updating ride package info:', error);
		return false;
	}
}

export async function updateRideWithMultipleFields(
	rideId: number,
	updateData: Record<string, any>
): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		console.log('🔄 Updating ride with multiple fields:', {
			rideId,
			updateData,
			url
		});

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(updateData)
		});

		console.log('📡 API Response:', {
			status: response.status,
			statusText: response.statusText,
			ok: response.ok
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('❌ Failed to update ride with multiple fields:', {
				status: response.status,
				statusText: response.statusText,
				errorBody: errorText
			});
			return false;
		}

		const responseData = await response.json();
		console.log('✅ Successfully updated ride:', responseData);
		return true;
	} catch (error) {
		console.error('💥 Error updating ride with multiple fields:', error);
		return false;
	}
}
