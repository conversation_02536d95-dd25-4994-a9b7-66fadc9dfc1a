import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Customer {
	id: number;
	name: string;
}

export async function fetchCustomers(skip: number = 0, limit: number = 1000): Promise<Customer[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.urlMsRides}/customers?skip=${skip}&limit=${limit}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return [];
	}
}
