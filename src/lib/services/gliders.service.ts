import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Glider {
	id: number;
	line: string;
	generation: string;
	number: string;
	name: string;
	pixhawkUuid: string;
	gliderMode: any;
	gliderStatus: any;
	autopilotSoftwareVersion: any;
	desiredAutopilotSoftwareVersion: any;
	jetsonSoftwareVersion: any;
	desiredJetsonSoftwareVersion: any;
	ftsPixhawkSoftwareVersion: any;
	desiredFtsPixhawkSoftwareVersion: any;
	ftsRaspiSoftwareVersion: any;
	desiredFtsRaspiSoftwareVersion: any;
	company: any;
	region: any;
	vpnIp: string;
	vpnNetworkId: string;
	manufacturingDate: string;
	registrationCode: string;
	registrationComplete: boolean;
	inUse: boolean;
	designDeviation: string;
	designComplianceRecord: string;
	totalFlightTimeInSeconds: number;
	totalFlightTimeSinceLastMaintenanceInSeconds: number;
	createdAt: string;
	updatedAt: string;
	flightPreConditionRead: any;
}

export async function fetchGliders(): Promise<Glider[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch gliders:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching gliders:', error);
		return [];
	}
}

export async function updateGlider(id: number, data: Partial<Glider>): Promise<Glider | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders/${id}`;

		const response = await fetch(url, {
			method: 'PUT',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to update glider:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error updating glider:', error);
		return null;
	}
}
