import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface RideStatus {
	id: number;
	name: string;
	color?: string;
	description?: string;
}

export async function fetchRideStatuses(): Promise<RideStatus[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.urlMsRides}/ride-statuses`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return [];
	}
}

export async function fetchRideStatusById(id: number): Promise<RideStatus | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/ride-statuses/${id}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return null;
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return null;
	}
}

export async function updateRideStatus(rideId: number, statusId: number): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify({
				ride_status_id: statusId
			})
		});

		if (!response.ok) {
			console.error('Failed to update ride status:', response.status, response.statusText);
			return false;
		}

		return true;
	} catch (error) {
		console.error('Error updating ride status:', error);
		return false;
	}
}

export function getStatusColor(status: RideStatus | string | number): string {
	if (typeof status === 'object' && status.color) {
		return status.color;
	}

	const statusName = typeof status === 'object' ? status.name : String(status);
	const lowerName = statusName.toLowerCase();

	if (lowerName.includes('scheduled') || lowerName.includes('pending')) {
		return 'blue';
	} else if (
		lowerName.includes('progress') ||
		lowerName.includes('active') ||
		lowerName.includes('ongoing')
	) {
		return 'yellow';
	} else if (
		lowerName.includes('completed') ||
		lowerName.includes('finished') ||
		lowerName.includes('done')
	) {
		return 'green';
	} else if (
		lowerName.includes('cancelled') ||
		lowerName.includes('canceled') ||
		lowerName.includes('failed')
	) {
		return 'red';
	} else if (lowerName.includes('delayed') || lowerName.includes('waiting')) {
		return 'orange';
	} else {
		return 'gray';
	}
}
