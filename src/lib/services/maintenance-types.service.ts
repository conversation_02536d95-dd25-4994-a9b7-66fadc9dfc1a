import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';
import type { MaintenanceType } from '../types/maintenance.types';

export async function fetchMaintenanceTypes(): Promise<MaintenanceType[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/maintenance-types`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return Array.isArray(data) ? data : [];
	} catch (error) {
		return [];
	}
}
