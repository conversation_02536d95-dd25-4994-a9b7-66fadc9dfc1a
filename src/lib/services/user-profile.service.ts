import { get } from 'svelte/store';
import { keycloakClient, userProfile, userRoles, isUserAdmin } from '../stores';
import { environment } from '../environment';
import type { KeycloakProfile } from 'keycloak-js';

export interface UserProfileData extends KeycloakProfile {
	roles?: string[];
	isAdmin?: boolean;
}

export class UserProfileService {
	static getUserInitials(profile: KeycloakProfile | null): string {
		if (!profile) return 'U';

		const firstName = profile.firstName || '';
		const lastName = profile.lastName || '';

		const firstInitial = firstName.charAt(0).toUpperCase();
		const lastInitial = lastName.charAt(0).toUpperCase();

		return firstInitial + lastInitial || profile.email?.charAt(0).toUpperCase() || 'U';
	}

	static getUserDisplayName(profile: KeycloakProfile | null): string {
		if (!profile) return '';

		const fullName = `${profile.firstName || ''} ${profile.lastName || ''}`.trim();
		return fullName || profile.email || '';
	}

	static getKeycloakAccountUrl(): string {
		const keycloak = get(keycloakClient);
		if (keycloak) {
			return `${environment.authUrl}/realms/${environment.authRealm}/account`;
		}
		return '#';
	}

	static async getCurrentUserProfile(): Promise<KeycloakProfile | null> {
		const keycloak = get(keycloakClient);
		if (!keycloak || !keycloak.authenticated) {
			return null;
		}

		try {
			const profile = await keycloak.loadUserProfile();
			userProfile.set(profile);
			return profile;
		} catch (error) {
			console.error('Failed to load user profile:', error);
			return null;
		}
	}

	static async getUserRoles(): Promise<string[]> {
		const keycloak = get(keycloakClient);
		if (!keycloak || !keycloak.authenticated) {
			userRoles.set([]);
			return [];
		}

		try {
			const realmRoles = keycloak.realmAccess?.roles || [];
			const resourceRoles = Object.values(keycloak.resourceAccess || {}).flatMap(
				(resource) => resource.roles || []
			);

			const allRoles = [...realmRoles, ...resourceRoles];
			userRoles.set(allRoles);
			return allRoles;
		} catch (error) {
			console.error('Failed to get user roles:', error);
			userRoles.set([]);
			return [];
		}
	}

	static async isUserAdmin(): Promise<boolean> {
		const roles = await this.getUserRoles();
		const adminStatus =
			roles.includes('admin') || roles.includes('realm-admin') || roles.includes('manage-users');
		isUserAdmin.set(adminStatus);
		return adminStatus;
	}

	static logout(): void {
		const keycloak = get(keycloakClient);
		if (keycloak) {
			localStorage.removeItem('kc_token');
			localStorage.removeItem('kc_refreshToken');
			userProfile.set(null);
			userRoles.set([]);
			isUserAdmin.set(false);
			this.clearUserDataFromLocalStorage();
			keycloak.logout();
		}
	}

	static storeUserDataInLocalStorage(profile: KeycloakProfile): void {
		try {
			const userData = {
				id: profile.id,
				email: profile.email,
				firstName: profile.firstName,
				lastName: profile.lastName,
				username: profile.username,
				timestamp: Date.now()
			};
			localStorage.setItem('user_profile', JSON.stringify(userData));
		} catch (error) {
			console.error('Failed to store user data in localStorage:', error);
		}
	}

	static getUserDataFromLocalStorage(): any | null {
		try {
			const userData = localStorage.getItem('user_profile');
			if (userData) {
				const parsed = JSON.parse(userData);
				const oneHour = 60 * 60 * 1000;
				if (Date.now() - parsed.timestamp < oneHour) {
					return parsed;
				}
				localStorage.removeItem('user_profile');
			}
		} catch (error) {
			console.error('Failed to get user data from localStorage:', error);
		}
		return null;
	}

	static clearUserDataFromLocalStorage(): void {
		localStorage.removeItem('user_profile');
	}
}
