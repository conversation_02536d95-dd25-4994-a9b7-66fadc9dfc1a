export interface Maintenance {
	id: number;
	equipmentId: number;
	equipment: Equipment;
	maintenanceTypeId: number;
	maintenanceType: MaintenanceType;
	dueDateTime: string; // ISO datetime format: "2025-06-04T11:17:51.291Z"
	completedDateTime?: string; // ISO datetime format: "2025-06-04T11:17:51.291Z"
	isScheduled: boolean;
	notes: string;
	maintenanceStaff: string;
	postMaintenanceChecklistDone: boolean;
	isSoftwareUpdate?: boolean;
	softwareUpdates?: SoftwareUpdateDetails;
	createdAt: string;
	updatedAt: string;
}

export interface Equipment {
	id: number;
	name: string; // Format: Mxx-yy (e.g., M01-15)
	status?: string;
	model?: string;
	serialNumber?: string;
	location?: string;
}

export interface MaintenanceType {
	id: number;
	name: string;
	description?: string;
	isScheduled?: boolean;
}

export interface SoftwareUpdateDetails {
	jetsonVersionId?: number;
	autopilotVersionId?: number;
	ftsPixhawkVersionId?: number;
	ftsRaspiVersionId?: number;
	forceSensorVersionId?: number;
}

export interface SoftwareVersion {
	id: number;
	name: string;
	version: string;
	type: string;
}

export interface MaintenanceCreateRequest {
	gliderId: number;
	mailboxId: number | null;
	dueDateTime: string; // ISO datetime format: "2025-06-04T11:17:51.291Z"
	completedDateTime?: string; // ISO datetime format: "2025-06-04T11:17:51.291Z"
	isScheduled: boolean;
	notes: string;
	maintenanceTypeId: number;
	maintenanceStaff: string;
	postMaintenanceChecklistDone: boolean;
	isSoftwareUpdate?: boolean;
	softwareUpdates?: SoftwareUpdateDetails;
}

export interface MaintenanceUpdateRequest extends Partial<MaintenanceCreateRequest> {
	id: number;
}

export interface MaintenanceResponse {
	data: Maintenance[] | Maintenance;
	success: boolean;
	message?: string;
	error?: string;
}
export interface MaintenanceFilters {
	equipmentId?: number;
	searchTerm?: string;
	dateFrom?: string;
	dateTo?: string;
	maintenanceTypeId?: number;
	isCompleted?: boolean;
	isScheduled?: boolean;
}

export interface MaintenanceSortConfig {
	field: keyof Maintenance;
	direction: 'asc' | 'desc';
}

export interface PaginationConfig {
	page: number;
	limit: number;
	total: number;
}
