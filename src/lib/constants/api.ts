import { environment } from '../environment';
import { browser } from '$app/environment';

export const getApiUrl = (endpoint: string): string => {
	const cleanEndpoint = endpoint.replace(/^\/?(api\/)?/, '');

	if (environment.production) {
		return `${environment.urlMsFleet}/${cleanEndpoint}`;
	} else {
		return `${environment.urlMsFleet}/${cleanEndpoint}`;
	}
};

export const API_ENDPOINTS = {
	ROUTES: {
		BASE: 'routes',
		BY_ID: (id: number) => `routes/${id}`
	},
	SOFTWARE: {
		BASE: 'software',
		VERSIONS: 'software-versions',
		BY_TYPE: (type: string) => `software-versions/${type}`
	},
	TIME_TRACKING: {
		START: 'time-tracking/start',
		STOP: 'time-tracking/stop'
	},
	SHIFTS: {
		LAST: 'shifts/last'
	},
	MAINTENANCE: {
		BASE: 'maintenances',
		BY_ID: (id: number) => `maintenances/${id}`
	},
	MAINTENANCE_TYPES: {
		BASE: 'maintenance-types'
	},
	EQUIPMENT: {
		BASE: 'equipment',
		BY_ID: (id: number) => `equipment/${id}`
	},
	STAFF: {
		BASE: 'staff'
	},
	GLIDERS: {
		BASE: 'gliders',
		BY_ID: (id: number) => `gliders/${id}`
	}
} as const;
