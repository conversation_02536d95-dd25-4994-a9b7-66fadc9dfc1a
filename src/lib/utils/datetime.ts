import { browser } from '$app/environment';
let cachedTimezone: string | null = null;

function getUserTimezone(): string {
	if (!browser) {
		return 'UTC';
	}
	if (cachedTimezone) {
		return cachedTimezone;
	}

	try {
		cachedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		return cachedTimezone;
	} catch {
		cachedTimezone = 'UTC';
		return cachedTimezone;
	}
}

export function formatDate(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return '-';
	try {
		const date = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		}).format(date);
	} catch {
		return '-';
	}
}

export function formatTime(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return '-';
	try {
		const date = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return '-';
	}
}
export function formatDateTime(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return 'N/A';
	try {
		const date = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return 'N/A';
	}
}

export function formatDateTimeDetailed(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return 'N/A';
	try {
		const date = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return 'N/A';
	}
}

export function formatTimeUTC(dateTimeString: string): string {
	if (!dateTimeString) return '-';
	try {
		const utcDate = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: 'UTC',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(utcDate);
	} catch {
		return '-';
	}
}
export function formatDateUTC(dateTimeString: string): string {
	if (!dateTimeString) return '-';
	try {
		const utcDate = new Date(dateTimeString);
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: 'UTC',
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		}).format(utcDate);
	} catch {
		return '-';
	}
}
