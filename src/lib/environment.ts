import { browser } from '$app/environment';

const getBoolean = (value: string | undefined) => {
	return value?.toLowerCase() === 'true' || value === '1';
};

function attributeFunction(attrName: string): string | null {
	if (!browser) return null;
	const rootElement = document.getElementById('root') || document.getElementById('app');
	return rootElement?.getAttribute(`data-${attrName}`) ?? null;
}

const BASE = attributeFunction('api-base-domain') ?? import.meta.env.VITE_API_BASE_DOMAIN;

const AUTH_CLIENT_ID = attributeFunction('auth-client-id') || import.meta.env.PUBLIC_AUTH_CLIENT_ID;
const AUTH_REALM = attributeFunction('auth-realm') || import.meta.env.PUBLIC_AUTH_REALM;
const AUTH_URL =
	attributeFunction('auth-url') || import.meta.env.PUBLIC_AUTH_URL || `https://auth.${BASE}`;

export const environment = {
	production: getBoolean(import.meta.env.VITE_PRODUCTION) || false,

	authClientId: browser ? attributeFunction('auth-client-id') || AUTH_CLIENT_ID : AUTH_CLIENT_ID,
	authUrl: browser ? attributeFunction('auth-url') || AUTH_URL : AUTH_URL,
	authRealm: browser ? attributeFunction('auth-realm') || AUTH_REALM : AUTH_REALM,

	flightReviewBackendUrl:
		import.meta.env.PUBLIC_FLIGHT_REVIEW_BACKEND_URL || `https://flight-review.${BASE}`,
	microsoftMsBackendUrl:
		import.meta.env.PUBLIC_MICROSOFT_MS_BACKEND_URL || `https://microsoft.${BASE}`,
	gliderMsBackendUrl: import.meta.env.PUBLIC_GLIDER_MS_BACKEND_URL || `https://glider.${BASE}/api`,

	urlMsFleet: import.meta.env.VITE_MS_FLEET_URL || `https://glider.${BASE}/api`,
	urlMsRides: import.meta.env.VITE_MS_RIDES_URL || `https://ride.${BASE}`,
	urlMsAuth: import.meta.env.VITE_MS_AUTH_URL || `https://auth.${BASE}`,
	urlIpConnector: import.meta.env.VITE_IP_CONNECTOR_URL || `https://ips.${BASE}`,
	urlNewbornGliderWard: import.meta.env.VITE_NEWBORN_URL || `https://newborn.${BASE}`,
	urlFlightReview: import.meta.env.VITE_FLIGHT_REVIEW_URL || `https://flight-review.${BASE}`,
	urlMicroSoft: import.meta.env.VITE_MICROSOFT_URL || `https://microsoft.${BASE}`,
	urlMainApp: import.meta.env.VITE_MAIN_APP_URL || `https://admin.${BASE}`,
	urlPkiApi: import.meta.env.VITE_PKI_API_URL || `https://vpn.${BASE}`,
	urlHealthCheck: import.meta.env.VITE_HEALTH_CHECK_URL || `https://ping.${BASE}`
};
