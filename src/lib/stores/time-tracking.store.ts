import { writable } from 'svelte/store';

export const trackingStore = writable({
	hasActiveTracking: false,
	activeRouteId: null as number | null,
	lastStateChange: Date.now()
});

export function resetTrackingErrors() {
	trackingStore.update((state) => ({
		...state,
		lastStateChange: Date.now()
	}));
}

export function setActiveRoute(routeId: number | null) {
	trackingStore.update((state) => ({
		hasActiveTracking: routeId !== null,
		activeRouteId: routeId,
		lastStateChange: Date.now()
	}));
}
