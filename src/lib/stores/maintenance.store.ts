import { writable } from 'svelte/store';
import type {
	Maintenance,
	MaintenanceFilters,
	MaintenanceSortConfig,
	PaginationConfig
} from '../types/maintenance.types';

export interface MaintenanceState {
	maintenances: Maintenance[];
	loading: boolean;
	error: string | null;
	filters: MaintenanceFilters;
	sort: MaintenanceSortConfig;
	pagination: PaginationConfig;
	selectedMaintenance: Maintenance | null;
}

const initialState: MaintenanceState = {
	maintenances: [],
	loading: false,
	error: null,
	filters: {},
	sort: {
		field: 'id',
		direction: 'desc'
	},
	pagination: {
		page: 1,
		limit: 10,
		total: 0
	},
	selectedMaintenance: null
};

export const maintenanceStore = writable<MaintenanceState>(initialState);

export function setMaintenances(maintenances: Maintenance[]) {
	maintenanceStore.update((state) => ({
		...state,
		maintenances,
		loading: false,
		error: null
	}));
}

export function setLoading(loading: boolean) {
	maintenanceStore.update((state) => ({
		...state,
		loading
	}));
}

export function setError(error: string | null) {
	maintenanceStore.update((state) => ({
		...state,
		error,
		loading: false
	}));
}

export function setFilters(filters: MaintenanceFilters) {
	maintenanceStore.update((state) => ({
		...state,
		filters,
		pagination: { ...state.pagination, page: 1 }
	}));
}

export function setSort(sort: MaintenanceSortConfig) {
	maintenanceStore.update((state) => ({
		...state,
		sort
	}));
}

export function setPagination(pagination: Partial<PaginationConfig>) {
	maintenanceStore.update((state) => ({
		...state,
		pagination: { ...state.pagination, ...pagination }
	}));
}

export function setSelectedMaintenance(maintenance: Maintenance | null) {
	maintenanceStore.update((state) => ({
		...state,
		selectedMaintenance: maintenance
	}));
}

export function addMaintenance(maintenance: Maintenance) {
	maintenanceStore.update((state) => ({
		...state,
		maintenances: [maintenance, ...state.maintenances]
	}));
}

export function updateMaintenance(updatedMaintenance: Maintenance) {
	maintenanceStore.update((state) => ({
		...state,
		maintenances: state.maintenances.map((m) =>
			m.id === updatedMaintenance.id ? updatedMaintenance : m
		)
	}));
}

export function removeMaintenance(id: number) {
	maintenanceStore.update((state) => ({
		...state,
		maintenances: state.maintenances.filter((m) => m.id !== id)
	}));
}

export function clearFilters() {
	maintenanceStore.update((state) => ({
		...state,
		filters: {},
		pagination: { ...state.pagination, page: 1 }
	}));
}

export function setSortState(sort: MaintenanceSortConfig) {
	maintenanceStore.update((state) => ({
		...state,
		sort
	}));
}

export function resetMaintenanceStore() {
	maintenanceStore.set(initialState);
}
