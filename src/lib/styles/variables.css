:root {
	--color-primary-50: #f0faf1;
	--color-primary-100: #dbf5dd;
	--color-primary-200: #b8e7bc;
	--color-primary-300: #8ed393;
	--color-primary-400: #64b86a;
	--color-primary-500: #48a851;
	--color-primary-600: #358a3d;
	--color-primary-700: #2b6e31;
	--color-primary-800: #245728;
	--color-primary-900: #1d4621;
	--color-primary-950: #0e2310;

	--color-gray-50: #f9fafb;
	--color-gray-100: #f3f4f6;
	--color-gray-200: #e5e7eb;
	--color-gray-300: #d1d5db;
	--color-gray-400: #9ca3af;
	--color-gray-500: #6b7280;
	--color-gray-600: #4b5563;
	--color-gray-700: #374151;
	--color-gray-800: #1f2937;
	--color-gray-900: #111827;
	--color-gray-950: #030712;

	--color-red-500: #ef4444;
	--color-red-600: #dc2626;
	--color-yellow-500: #eab308;
	--color-white: #ffffff;
	--color-black: #000000;

	--color-success: var(--color-primary-500);
	--color-error: var(--color-red-600);
	--color-warning: var(--color-yellow-500);
	--color-info: var(--color-gray-600);

	--color-text-primary: var(--color-gray-900);
	--color-text-secondary: var(--color-gray-600);
	--color-text-tertiary: var(--color-gray-500);
	--color-text-disabled: var(--color-gray-400);

	--transition-fast: 150ms;
	--transition-normal: 300ms;
	--transition-slow: 500ms;
	--transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}
