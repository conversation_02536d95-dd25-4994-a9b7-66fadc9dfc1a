.common-spinner {
	display: inline-block;
	color: var(--color-primary-500);
	animation: spin 1s linear infinite;
}

.spinner-xs {
	height: var(--font-size-xs);
	width: var(--font-size-xs);
}

.spinner-sm {
	height: var(--font-size-sm);
	width: var(--font-size-sm);
}

.spinner-md {
	height: var(--font-size-base);
	width: var(--font-size-base);
}

.spinner-lg {
	height: var(--font-size-lg);
	width: var(--font-size-lg);
}

.spinner-xl {
	height: var(--font-size-xl);
	width: var(--font-size-xl);
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.spinner-light {
	color: var(--color-white);
}

.spinner-dark {
	color: var(--color-gray-800);
}

.spinner-primary {
	color: var(--color-primary-500);
}

.spinner-secondary {
	color: var(--color-gray-500);
}

.spinner-with-text {
	display: inline-flex;
	align-items: center;
	gap: var(--spacing-2);
}

.spinner-text {
	font-size: var(--font-size-sm);
	color: inherit;
}

.spinner-container-full {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100%;
	width: 100%;
}

.spinner-container-center {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: var(--spacing-4);
}
