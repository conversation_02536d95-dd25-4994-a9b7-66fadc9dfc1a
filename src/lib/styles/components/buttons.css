.btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
	cursor: pointer;
	border: none;
	text-decoration: none;
}

.btn-primary {
	background-color: var(--color-primary-500);
	color: var(--color-white);
	transition: var(--transition-normal);
}

.btn-primary:hover:not(:disabled) {
	background-color: var(--color-primary-600);
	transform: translateY(-1px);
	box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled) {
	transform: translateY(0);
}

.btn-primary:focus-visible {
	outline: 2px solid var(--color-primary-500);
	outline-offset: 2px;
}

.btn-primary:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.btn-secondary {
	background-color: var(--color-gray-100);
	color: var(--color-gray-600);
	border: 1px solid var(--color-gray-200);
	transition: var(--transition-normal);
}

.btn-secondary:hover:not(:disabled) {
	background-color: var(--color-gray-200);
	color: var(--color-gray-800);
}

.btn-secondary:focus-visible {
	outline: 2px solid var(--color-gray-400);
	outline-offset: 2px;
}

.btn-text {
	background: transparent;
	color: var(--color-primary-500);
	padding: 0;
	border: none;
	transition: var(--transition-fast);
}

.btn-text:hover {
	color: var(--color-primary-600);
	text-decoration: underline;
}

.btn-text:focus-visible {
	outline: 2px solid var(--color-primary-200);
	outline-offset: 2px;
}

.btn-xs {
	font-size: var(--font-size-xs);
	padding: var(--spacing-1) var(--spacing-2);
	border-radius: var(--radius-sm);
	gap: var(--spacing-1);
}

.btn-sm {
	font-size: var(--font-size-sm);
	padding: var(--spacing-1) var(--spacing-3);
	border-radius: var(--radius-md);
	gap: var(--spacing-1);
}

.btn-md {
	font-size: var(--font-size-base);
	padding: var(--spacing-2) var(--spacing-4);
	border-radius: var(--radius-md);
	gap: var(--spacing-2);
}

.btn-lg {
	font-size: var(--font-size-lg);
	padding: var(--spacing-3) var(--spacing-5);
	border-radius: var(--radius-lg);
	gap: var(--spacing-2);
}

.btn-icon {
	padding: var(--spacing-2);
	border-radius: var(--radius-full);
}

.floating-action-btn {
	background-color: var(--color-primary-600);
	color: var(--color-white);
	border-radius: var(--radius-full);
	transition: var(--transition-slow);
	padding: var(--spacing-3) var(--spacing-4);
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: var(--spacing-2);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(8px);
	position: fixed;
	bottom: var(--spacing-8);
	right: var(--spacing-8);
	z-index: var(--z-fixed);
}

.floating-action-btn:hover:not(:disabled) {
	background-color: var(--color-primary-700);
	transform: translateY(-3px);
	box-shadow: var(--shadow-xl);
}

.floating-action-btn:active:not(:disabled) {
	transform: translateY(0) scale(0.98);
	box-shadow: var(--shadow-md);
}

.floating-action-btn:disabled {
	opacity: 0.7;
	cursor: not-allowed;
	box-shadow: none;
}

@keyframes slide-up {
	from {
		transform: translateY(20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.floating-action-btn {
	animation: slide-up 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}
