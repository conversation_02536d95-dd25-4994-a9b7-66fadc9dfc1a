.table-container {
	width: 100%;
	overflow-x: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-sm);
}

.table {
	width: 100%;
	border-collapse: separate;
	border-spacing: 0;
	font-size: var(--font-size-sm);
}

.table-head {
	background-color: var(--color-gray-50);
}

.table-head-cell {
	padding: var(--spacing-3) var(--spacing-4);
	font-weight: var(--font-weight-medium);
	color: var(--color-gray-700);
	text-align: left;
	border-bottom: 1px solid var(--color-gray-200);
	white-space: nowrap;
}

.table-head-cell-sorted {
	color: var(--color-primary-600);
}

.table-body-row {
	transition: background-color 0.2s ease;
}

.table-body-row:hover {
	background-color: var(--color-gray-50);
}

.table-body-cell {
	padding: var(--spacing-3) var(--spacing-4);
	border-bottom: 1px solid var(--color-gray-200);
	color: var(--color-gray-800);
}

.table-hoverable .table-body-row:hover {
	background-color: var(--color-gray-100);
}

.table-striped .table-body-row:nth-child(even) {
	background-color: var(--color-gray-50);
}

.table-bordered {
	border: 1px solid var(--color-gray-200);
}

.table-compact .table-head-cell,
.table-compact .table-body-cell {
	padding: var(--spacing-2);
}

.cell-numeric {
	text-align: right;
}

.cell-centered {
	text-align: center;
}

.cell-status {
	white-space: nowrap;
}

.table-empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: var(--spacing-8);
	color: var(--color-gray-500);
	text-align: center;
}

.checkbox-hover {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	width: 100%;
	padding: var(--spacing-1);
	border-radius: var(--radius-sm);
	transition: background-color 0.2s ease;
}

.checkbox-hover:hover {
	background-color: var(--color-gray-100);
	cursor: pointer;
}
