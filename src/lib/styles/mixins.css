.text-title-lg {
	font-size: 1.5rem;
	font-weight: 700;
	line-height: 1.2;
	color: var(--color-text-primary);
}

.text-title-md {
	font-size: 1.25rem;
	font-weight: 600;
	line-height: 1.25;
	color: var(--color-text-primary);
}

.text-title-sm {
	font-size: 1.125rem;
	font-weight: 600;
	line-height: 1.3;
	color: var(--color-text-secondary);
}

.text-body {
	font-size: 1rem;
	line-height: 1.5;
	color: var(--color-text-secondary);
}

.text-body-sm {
	font-size: 0.875rem;
	line-height: 1.5;
	color: var(--color-text-tertiary);
}

.text-caption {
	font-size: 0.75rem;
	line-height: 1.4;
	color: var(--color-text-tertiary);
}

.floating-action-btn {
	background-color: var(--color-primary-600);
	color: var(--color-white);
	border-radius: 9999px;
	transition: 500ms cubic-bezier(0.4, 0, 0.2, 1);
	padding: 0.75rem 1rem;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	box-shadow:
		0 10px 15px -3px rgba(0, 0, 0, 0.1),
		0 4px 6px -2px rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(8px);
	width: auto;
	height: auto;
	border: none;
	position: relative;
	min-width: 8rem;
}

.floating-action-btn:hover:not(:disabled) {
	background-color: var(--color-primary-700);
	transform: translateY(-3px);
	box-shadow:
		0 20px 25px -5px rgba(0, 0, 0, 0.1),
		0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.floating-action-btn:active:not(:disabled) {
	transform: translateY(0) scale(0.98);
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.floating-action-btn:disabled {
	opacity: 0.7;
	cursor: not-allowed;
	box-shadow: none;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(72, 168, 81, 0.4);
	}
	70% {
		box-shadow: 0 0 0 8px rgba(72, 168, 81, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(72, 168, 81, 0);
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideDown {
	from {
		transform: translateY(-20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
	animation: fadeIn 0.3s ease-in;
}

.animate-slide-up {
	animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
	animation: slideDown 0.3s ease-out;
}

.animate-spin {
	animation: spin 1s linear infinite;
}
