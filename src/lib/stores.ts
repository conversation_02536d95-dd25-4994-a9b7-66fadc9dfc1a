import { writable, type Writable } from 'svelte/store';
import type { KeycloakProfile } from 'keycloak-js';
import Keycloak from 'keycloak-js';

export interface ExtendedKeycloakProfile extends KeycloakProfile {
	email: string;
	firstName: string;
	lastName: string;
}

export const userProfile: Writable<ExtendedKeycloakProfile | null> = writable(null);
export const keycloakClient: Writable<Keycloak | null> = writable(null);
export const userRoles: Writable<string[]> = writable([]);
export const isUserAdmin: Writable<boolean> = writable(false);

export const formDataCustom = writable({});
