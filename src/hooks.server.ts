import type { Handle } from '@sveltejs/kit';

export const handle: Handle = async ({ event, resolve }) => {
	return await resolve(event, {
		transformPageChunk: async ({ html }) => {
			const env = await import('$env/static/public');
			return html
				.replace(
					'data-api-base-domain-replacement',
					env.PUBLIC_API_BASE_DOMAIN ?? '$PUBLIC_API_BASE_DOMAIN'
				)
				.replace(
					'data-flight-review-backend-url-replacement',
					env.PUBLIC_FLIGHT_REVIEW_BACKEND_URL ?? '$PUBLIC_FLIGHT_REVIEW_BACKEND_URL'
				)
				.replace(
					'data-microsoft-ms-backend-url-replacement',
					env.PUBLIC_MICROSOFT_MS_BACKEND_URL ?? '$PUBLIC_MICROSOFT_MS_BACKEND_URL'
				)
				.replace(
					'data-glider-ms-backend-url-replacement',
					env.PUBLIC_GLIDER_MS_BACKEND_URL ?? '$PUBLIC_GLIDER_MS_BACKEND_URL'
				)
				.replace(
					'data-auth-client-id-replacement',
					env.PUBLIC_AUTH_CLIENT_ID ?? '$PUBLIC_AUTH_CLIENT_ID'
				)
				.replace('data-auth-realm-replacement', env.PUBLIC_AUTH_REALM ?? '$PUBLIC_AUTH_REALM')
				.replace('data-auth-url-replacement', env.PUBLIC_AUTH_URL ?? '$PUBLIC_AUTH_URL');
		}
	});
};
