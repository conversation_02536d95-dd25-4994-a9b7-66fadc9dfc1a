{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "strict": true, "composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "target": "ESNext", "allowSyntheticDefaultImports": true, "verbatimModuleSyntax": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["svelte", "vite/client"]}, "include": ["src/**/*.d.ts", "src/**/*.ts", "src/**/*.js", "src/**/*.svelte"], "references": [{"path": "./tsconfig.node.json"}]}