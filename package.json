{"name": "glider-admin-ui", "version": "0.0.1", "type": "module", "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "sideEffects": ["**/*.css"], "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"svelte": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "prepack": "svelte-kit sync && svelte-package && publint", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test:unit:run": "vitest --run", "test:e2e": "start-server-and-test 'dev' http://localhost:5173 'playwright test'", "test": "npm run test:unit:run && npm run test:e2e", "test:ci": "npm run test"}, "peerDependencies": {"@sveltejs/kit": "^2.17.1", "svelte": "^5.0.0"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@event-calendar/core": "^3.10.0", "@event-calendar/interaction": "^3.10.0", "@event-calendar/resource-time-grid": "^3.10.0", "@event-calendar/resource-timeline": "^3.10.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.17.3", "@sveltejs/package": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.0", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^2.46.1", "globals": "^15.14.0", "jsdom": "^26.1.0", "postcss": "^8.5.1", "postcss-load-config": "^6.0.1", "postcss-nesting": "^13.0.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "publint": "^0.3.2", "sass": "^1.83.4", "start-server-and-test": "^2.0.12", "svelte": "^5.20.4", "svelte-check": "^4.0.0", "svelte-preprocess": "^6.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0", "vitest": "^3.0.0"}, "dependencies": {"@sveltestack/svelte-query": "^1.6.0", "flowbite": "^3.1.1", "flowbite-svelte": "^0.47.4", "flowbite-svelte-icons": "^2.0.2", "sonner": "^2.0.1", "keycloak-js": "^26.2.0", "svelte-sonner": "^0.3.28", "svelte-file-dropzone": "^2.0.9", "xlsx": "^0.18.5", "yup": "^1.6.1"}}