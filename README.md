# Glider Admin UI

A SvelteKit-based admin interface for the Glider management system, providing authentication via Keycloak and integration with various microservices.

## Prerequisites

- Node.js 18+
- npm or pnpm
- Access to Keycloak authentication server

## Environment Configuration

Before running the application, you need to set up environment variables. Copy the `.env.example` file to `.env` and configure the following variables:

### Required Environment Variables

```bash
# API Configuration
VITE_API_BASE_DOMAIN=uphi.cc
VITE_PRODUCTION=false

# Microservice URLs (Build-time)
VITE_MS_FLEET_URL=https://glider.uphi.cc/api
VITE_MS_RIDES_URL=https://ride.uphi.cc
VITE_MS_AUTH_URL=https://auth.uphi.cc
VITE_IP_CONNECTOR_URL=https://ips.uphi.cc
VITE_NEWBORN_URL=https://newborn.uphi.cc
VITE_FLIGHT_REVIEW_URL=https://flight-review.uphi.cc
VITE_MICROSOFT_URL=https://microsoft.uphi.cc

# Authentication (Runtime)
PUBLIC_AUTH_CLIENT_ID=admin-ui
PUBLIC_AUTH_REALM=jedsy
PUBLIC_AUTH_URL=https://auth.uphi.cc

# Backend URLs (Runtime)
PUBLIC_GLIDER_MS_BACKEND_URL=https://glider.uphi.cc/api
PUBLIC_MICROSOFT_MS_BACKEND_URL=https://microsoft.uphi.cc
PUBLIC_FLIGHT_REVIEW_BACKEND_URL=https://flight-review.uphi.cc
PUBLIC_API_BASE_DOMAIN=uphi.cc
```

### Environment Variable Types

- **VITE\_\*** variables are available at build time and embedded in the client bundle
- **PUBLIC\_\*** variables are available at runtime on both server and client

## Setup Instructions

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd glider-admin-ui
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Configure environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your specific configuration
   ```

4. **Configure Keycloak Client**

   In your Keycloak admin console (`https://auth.uphi.cc/`):
   - Navigate to realm `jedsy`
   - Go to Clients → `svelte-app-test`
   - Add `http://localhost:5174/*` to Valid Redirect URIs
   - Save the configuration

## Development

Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:5173/` (or the next available port).

### Development Notes

- The application uses Keycloak for authentication
- Clear browser localStorage if you encounter authentication issues
- The app runs in SPA mode (`ssr = false`) for client-side authentication

## Building

Create a production build:

```bash
npm run build
```

Preview the production build:

```bash
npm run preview
```

## Docker Deployment

The application includes Docker configuration for production deployment:

```bash
# Build the Docker image
docker build -t glider-admin-ui .

# Run the container
docker run -p 8080:8080 glider-admin-ui
```

### Environment Variables in Docker

When deploying with Docker, you can pass environment variables at build time:

```bash
docker build \
  --build-arg VITE_PRODUCTION=true \
  --build-arg VITE_MS_FLEET_URL=https://glider.uphi.cc/api \
  --build-arg VITE_MS_AUTH_URL=https://auth.uphi.cc \
  -t glider-admin-ui .
```

## Project Structure

```
src/
├── lib/
│   ├── components/     # Reusable UI components
│   ├── services/       # API service layers
│   ├── stores/         # Svelte stores
│   ├── types/          # TypeScript type definitions
│   └── environment.ts  # Environment configuration
├── routes/             # SvelteKit routes
└── styles/             # Global styles and variables
```

## Features

- **Authentication**: Keycloak integration with role-based access
- **Software Management**: Version tracking and updates
- **Route Management**: Time tracking and route configuration
- **Maintenance**: Equipment maintenance scheduling
- **File Upload**: Flight schedule upload functionality
- **Responsive Design**: Mobile-friendly interface using Tailwind CSS

## Troubleshooting

### Authentication Issues

1. **Invalid redirect_uri error**:
   - Ensure Keycloak client has correct redirect URI configured
   - Check that the development server port matches the configured URI

2. **Token expired errors**:
   - Clear browser localStorage
   - Restart the development server

3. **Environment variables not loading**:
   - Restart the development server after changing `.env`
   - Ensure variable names use correct prefixes (`VITE_` or `PUBLIC_`)

### Development Server Issues

- If port 5174 is in use, Vite will automatically use the next available port
- Update Keycloak redirect URI if the port changes
