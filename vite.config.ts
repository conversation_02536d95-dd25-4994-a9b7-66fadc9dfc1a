import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), '');

	return {
		plugins: [sveltekit()],
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: ''
				}
			}
		},
		resolve: {
			alias: {
				$styles: '/src/styles',
				$lib: '/src/lib',
				$nm: '/node_modules'
			}
		},
		optimizeDeps: {
			exclude: ['@event-calendar/resource-timeline']
		},
		ssr: {
			noExternal: ['@event-calendar/core', '@event-calendar/resource-timeline']
		},
		server: {
			port: 5173,
			proxy: {
				'/shifts': {
					target: env.VITE_MS_RIDES_URL || 'https://ride.uphi.cc',
					changeOrigin: true,
					secure: false,
					bypass: (req) => {
						const isApiRequest =
							req.url.startsWith('/shifts/') &&
							!req.headers.accept?.includes('text/html') &&
							!req.url.includes('__data.json');

						if (!isApiRequest) {
							return '/';
						}
					}
				},
				'/time-tracking': {
					target: env.VITE_MS_RIDES_URL || 'https://ride.uphi.cc',
					changeOrigin: true,
					secure: false
				},
				'/api/shifts': {
					target: env.VITE_MS_RIDES_URL || 'https://ride.uphi.cc',
					changeOrigin: true,
					secure: false,
					rewrite: (path) => path.replace(/^\/api\/shifts/, '/shifts')
				},
				'/api': {
					target: env.VITE_MS_FLEET_URL || 'https://glider.uphi.cc/api',
					changeOrigin: true,
					secure: false,
					rewrite: (path) => path.replace(/^\/api/, '/api')
				}
			}
		}
	};
});
