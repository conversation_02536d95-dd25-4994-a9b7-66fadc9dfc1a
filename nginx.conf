server {
    listen 8080;
    server_name localhost;
    port_in_redirect off;
    root /static;

    location /static/ {
        expires 1y;
        add_header Cache-Control "public";
    }

    location / {
        add_header Cache-Control no-store always;

        # Remove trailing slash for paths like /routes/ => /routes
        rewrite ^(.+)/$ $1?$args permanent;


        try_files $uri $uri.html /index.html;
    }
}
