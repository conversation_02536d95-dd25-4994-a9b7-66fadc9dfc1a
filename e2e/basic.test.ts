import { expect, test } from '@playwright/test';

test('homepage loads correctly', async ({ page }) => {
	await page.goto('/', { waitUntil: 'networkidle' });

	await page.waitForTimeout(2000);

	await page.waitForFunction(() => {
		return document.readyState === 'complete';
	});

	const title = await page.title();
	expect(title.length).toBeGreaterThan(0);

	await expect(page.locator('body')).toBeVisible();
});

test('routes page is accessible', async ({ page }) => {
	await page.goto('/routes', { waitUntil: 'networkidle' });

	await page.waitForTimeout(2000);

	// Should redirect to login or show routes page
	// Since we don't have auth in tests, we expect some content
	await expect(page.locator('body')).toBeVisible();
});

test('application structure is correct', async ({ page }) => {
	await page.goto('/', { waitUntil: 'domcontentloaded' });

	// Check for basic application structure
	await expect(page.locator('html')).toBeVisible();
	await expect(page.locator('body')).toBeVisible();

	// Check for app div
	const appDiv = page.locator('#app');
	if (await appDiv.isVisible()) {
		await expect(appDiv).toBeVisible();
	}
});
